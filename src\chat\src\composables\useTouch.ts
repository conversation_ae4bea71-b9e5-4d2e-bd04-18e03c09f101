/**
 * 触摸手势处理钩子
 * 提供移动端友好的触摸交互功能
 */

import { ref, onMounted, onUnmounted, readonly, watch, type Ref } from 'vue'

interface TouchPoint {
  x: number
  y: number
  timestamp: number
}

interface SwipeDirection {
  direction: 'left' | 'right' | 'up' | 'down'
  distance: number
  duration: number
  velocity: number
}

interface UseTouchOptions {
  threshold?: number // 最小滑动距离
  maxDuration?: number // 最大滑动时间
  preventScroll?: boolean // 是否阻止默认滚动
}

interface UseTouchReturn {
  // 状态
  isTouching: Readonly<Ref<boolean>>
  touchStart: Readonly<Ref<TouchPoint | null>>
  touchEnd: Readonly<Ref<TouchPoint | null>>

  // 事件处理
  onTouchStart: (callback: (point: TouchPoint) => void) => void
  onTouchMove: (callback: (point: TouchPoint) => void) => void
  onTouchEnd: (callback: (point: TouchPoint) => void) => void
  onSwipe: (callback: (swipe: SwipeDirection) => void) => void
  onTap: (callback: (point: TouchPoint) => void) => void
  onLongPress: (callback: (point: TouchPoint) => void) => void

  // 方法
  bindElement: (element: HTMLElement) => void
  unbindElement: () => void
}

export function useTouch(options: UseTouchOptions = {}): UseTouchReturn {
  const {
    threshold = 50,
    maxDuration = 500,
    preventScroll = false
  } = options

  // 响应式状态
  const isTouching = ref(false)
  const touchStart = ref<TouchPoint | null>(null)
  const touchEnd = ref<TouchPoint | null>(null)
  const boundElement = ref<HTMLElement | null>(null)

  // 事件回调
  const touchStartCallbacks = ref<Array<(point: TouchPoint) => void>>([])
  const touchMoveCallbacks = ref<Array<(point: TouchPoint) => void>>([])
  const touchEndCallbacks = ref<Array<(point: TouchPoint) => void>>([])
  const swipeCallbacks = ref<Array<(swipe: SwipeDirection) => void>>([])
  const tapCallbacks = ref<Array<(point: TouchPoint) => void>>([])
  const longPressCallbacks = ref<Array<(point: TouchPoint) => void>>([])

  // 长按定时器
  let longPressTimer: number | null = null
  const longPressDelay = 500 // 长按延迟时间

  // 获取触摸点坐标
  const getTouchPoint = (event: TouchEvent): TouchPoint => {
    const touch = event.touches[0] || event.changedTouches[0]
    return {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    }
  }

  // 计算两点间距离
  const getDistance = (point1: TouchPoint, point2: TouchPoint): number => {
    const dx = point2.x - point1.x
    const dy = point2.y - point1.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  // 计算滑动方向
  const getSwipeDirection = (start: TouchPoint, end: TouchPoint): SwipeDirection => {
    const dx = end.x - start.x
    const dy = end.y - start.y
    const distance = getDistance(start, end)
    const duration = end.timestamp - start.timestamp
    const velocity = distance / duration

    let direction: 'left' | 'right' | 'up' | 'down'

    if (Math.abs(dx) > Math.abs(dy)) {
      direction = dx > 0 ? 'right' : 'left'
    } else {
      direction = dy > 0 ? 'down' : 'up'
    }

    return {
      direction,
      distance,
      duration,
      velocity
    }
  }

  // 触摸开始事件处理
  const handleTouchStart = (event: TouchEvent) => {
    if (preventScroll) {
      event.preventDefault()
    }

    const point = getTouchPoint(event)
    touchStart.value = point
    isTouching.value = true

    // 触发回调
    touchStartCallbacks.value.forEach(callback => callback(point))

    // 开始长按检测
    longPressTimer = window.setTimeout(() => {
      if (isTouching.value && touchStart.value) {
        longPressCallbacks.value.forEach(callback => callback(touchStart.value!))
      }
    }, longPressDelay)
  }

  // 触摸移动事件处理
  const handleTouchMove = (event: TouchEvent) => {
    if (!isTouching.value || !touchStart.value) return

    if (preventScroll) {
      event.preventDefault()
    }

    const point = getTouchPoint(event)

    // 如果移动距离超过阈值，取消长按
    const distance = getDistance(touchStart.value, point)
    if (distance > 10 && longPressTimer) {
      clearTimeout(longPressTimer)
      longPressTimer = null
    }

    // 触发回调
    touchMoveCallbacks.value.forEach(callback => callback(point))
  }

  // 触摸结束事件处理
  const handleTouchEnd = (event: TouchEvent) => {
    if (!isTouching.value || !touchStart.value) return

    const point = getTouchPoint(event)
    touchEnd.value = point
    isTouching.value = false

    // 清除长按定时器
    if (longPressTimer) {
      clearTimeout(longPressTimer)
      longPressTimer = null
    }

    // 触发回调
    touchEndCallbacks.value.forEach(callback => callback(point))

    // 检测滑动
    const distance = getDistance(touchStart.value, point)
    const duration = point.timestamp - touchStart.value.timestamp

    if (distance >= threshold && duration <= maxDuration) {
      // 是滑动手势
      const swipe = getSwipeDirection(touchStart.value, point)
      swipeCallbacks.value.forEach(callback => callback(swipe))
    } else if (distance < 10 && duration < 300) {
      // 是点击手势
      tapCallbacks.value.forEach(callback => callback(point))
    }

    // 重置状态
    touchStart.value = null
    touchEnd.value = null
  }

  // 绑定元素
  const bindElement = (element: HTMLElement) => {
    if (boundElement.value) {
      unbindElement()
    }

    boundElement.value = element

    element.addEventListener('touchstart', handleTouchStart, { passive: !preventScroll })
    element.addEventListener('touchmove', handleTouchMove, { passive: !preventScroll })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })
    element.addEventListener('touchcancel', handleTouchEnd, { passive: true })
  }

  // 解绑元素
  const unbindElement = () => {
    if (!boundElement.value) return

    boundElement.value.removeEventListener('touchstart', handleTouchStart)
    boundElement.value.removeEventListener('touchmove', handleTouchMove)
    boundElement.value.removeEventListener('touchend', handleTouchEnd)
    boundElement.value.removeEventListener('touchcancel', handleTouchEnd)

    boundElement.value = null
  }

  // 注册事件回调
  const onTouchStart = (callback: (point: TouchPoint) => void) => {
    touchStartCallbacks.value.push(callback)
  }

  const onTouchMove = (callback: (point: TouchPoint) => void) => {
    touchMoveCallbacks.value.push(callback)
  }

  const onTouchEnd = (callback: (point: TouchPoint) => void) => {
    touchEndCallbacks.value.push(callback)
  }

  const onSwipe = (callback: (swipe: SwipeDirection) => void) => {
    swipeCallbacks.value.push(callback)
  }

  const onTap = (callback: (point: TouchPoint) => void) => {
    tapCallbacks.value.push(callback)
  }

  const onLongPress = (callback: (point: TouchPoint) => void) => {
    longPressCallbacks.value.push(callback)
  }

  // 清理
  onUnmounted(() => {
    unbindElement()
    if (longPressTimer) {
      clearTimeout(longPressTimer)
    }
  })

  return {
    // 状态
    isTouching: readonly(isTouching),
    touchStart: readonly(touchStart),
    touchEnd: readonly(touchEnd),

    // 事件处理
    onTouchStart,
    onTouchMove,
    onTouchEnd,
    onSwipe,
    onTap,
    onLongPress,

    // 方法
    bindElement,
    unbindElement
  }
}

// 预设的手势配置
export const touchPresets = {
  // 滑动删除
  swipeToDelete: {
    threshold: 100,
    maxDuration: 300,
    preventScroll: false
  },

  // 拖拽排序
  dragToSort: {
    threshold: 20,
    maxDuration: 1000,
    preventScroll: true
  },

  // 快速滑动
  quickSwipe: {
    threshold: 50,
    maxDuration: 200,
    preventScroll: false
  }
}

// 工具函数：检测是否为移动设备
export const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 工具函数：检测是否支持触摸
export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}
