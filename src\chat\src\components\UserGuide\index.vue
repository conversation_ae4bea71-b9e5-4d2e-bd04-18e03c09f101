<!--
  用户引导组件
  为新用户提供交互式的功能引导
-->
<template>
  <div v-if="visible" class="user-guide-overlay">
    <!-- 遮罩层 -->
    <div class="guide-backdrop" @click="handleBackdropClick">
      <!-- 高亮区域 -->
      <div 
        v-if="currentStep"
        class="highlight-area"
        :style="highlightStyle"
      ></div>
    </div>

    <!-- 引导卡片 -->
    <div 
      v-if="currentStep"
      class="guide-card"
      :style="cardStyle"
    >
      <!-- 箭头指示器 -->
      <div 
        class="guide-arrow"
        :class="arrowClass"
      ></div>

      <!-- 卡片内容 -->
      <div class="guide-content">
        <!-- 步骤指示器 -->
        <div class="step-indicator">
          <div class="step-dots">
            <div
              v-for="(step, index) in steps"
              :key="step.id"
              class="step-dot"
              :class="{
                'active': index === currentStepIndex,
                'completed': index < currentStepIndex
              }"
            ></div>
          </div>
          <div class="step-counter">
            {{ currentStepIndex + 1 }} / {{ steps.length }}
          </div>
        </div>

        <!-- 标题和描述 -->
        <div class="guide-header">
          <h3 class="guide-title">{{ currentStep.title }}</h3>
          <p class="guide-description">{{ currentStep.description }}</p>
        </div>

        <!-- 图片或动画 -->
        <div v-if="currentStep.image || currentStep.animation" class="guide-media">
          <img 
            v-if="currentStep.image"
            :src="currentStep.image"
            :alt="currentStep.title"
            class="guide-image"
          />
          <div 
            v-else-if="currentStep.animation"
            class="guide-animation"
            v-html="currentStep.animation"
          ></div>
        </div>

        <!-- 操作按钮 -->
        <div class="guide-actions">
          <button
            v-if="currentStepIndex > 0"
            class="guide-button guide-button-secondary"
            @click="previousStep"
          >
            上一步
          </button>
          
          <button
            v-if="!isLastStep"
            class="guide-button guide-button-primary"
            @click="nextStep"
          >
            {{ currentStep.actionText || '下一步' }}
          </button>
          
          <button
            v-else
            class="guide-button guide-button-primary"
            @click="finishGuide"
          >
            开始使用
          </button>
          
          <button
            class="guide-button guide-button-text"
            @click="skipGuide"
          >
            跳过引导
          </button>
        </div>
      </div>
    </div>

    <!-- 快速操作提示 -->
    <div v-if="showQuickTips" class="quick-tips">
      <div class="quick-tips-content">
        <div class="quick-tips-header">
          <h4>💡 快速提示</h4>
          <button class="close-tips" @click="hideQuickTips">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
        <div class="tips-list">
          <div v-for="tip in quickTips" :key="tip.id" class="tip-item">
            <div class="tip-icon">{{ tip.icon }}</div>
            <div class="tip-text">{{ tip.text }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

interface GuideStep {
  id: string
  title: string
  description: string
  target?: string // CSS选择器
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center'
  image?: string
  animation?: string
  actionText?: string
  onEnter?: () => void
  onExit?: () => void
}

interface QuickTip {
  id: string
  icon: string
  text: string
}

interface Props {
  visible?: boolean
  steps?: GuideStep[]
  autoStart?: boolean
  showQuickTips?: boolean
  allowSkip?: boolean
  backdropDismiss?: boolean
}

interface Emits {
  (e: 'finish'): void
  (e: 'skip'): void
  (e: 'step-change', step: GuideStep, index: number): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  steps: () => [],
  autoStart: true,
  showQuickTips: true,
  allowSkip: true,
  backdropDismiss: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const currentStepIndex = ref(0)
const targetElement = ref<HTMLElement | null>(null)

// 快速提示
const quickTips = ref<QuickTip[]>([
  { id: '1', icon: '⌨️', text: '按 Enter 发送消息，Shift+Enter 换行' },
  { id: '2', icon: '📎', text: '点击附件按钮上传文件' },
  { id: '3', icon: '🔍', text: '使用 @ 符号搜索AI应用' },
  { id: '4', icon: '🎯', text: '长按消息可以复制或重新生成' }
])

// 计算属性
const currentStep = computed(() => {
  return props.steps[currentStepIndex.value]
})

const isLastStep = computed(() => {
  return currentStepIndex.value === props.steps.length - 1
})

const highlightStyle = computed(() => {
  if (!targetElement.value) return {}

  const rect = targetElement.value.getBoundingClientRect()
  const padding = 8

  return {
    position: 'fixed',
    top: `${rect.top - padding}px`,
    left: `${rect.left - padding}px`,
    width: `${rect.width + padding * 2}px`,
    height: `${rect.height + padding * 2}px`,
    borderRadius: '8px',
    boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)',
    pointerEvents: 'none',
    zIndex: 9998
  }
})

const cardStyle = computed(() => {
  if (!targetElement.value) {
    // 居中显示
    return {
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      zIndex: 9999
    }
  }

  const rect = targetElement.value.getBoundingClientRect()
  const cardWidth = 320
  const cardHeight = 200
  const spacing = 20

  let top = rect.bottom + spacing
  let left = rect.left + rect.width / 2 - cardWidth / 2

  // 边界检查
  if (top + cardHeight > window.innerHeight) {
    top = rect.top - cardHeight - spacing
  }
  if (left < 20) {
    left = 20
  }
  if (left + cardWidth > window.innerWidth - 20) {
    left = window.innerWidth - cardWidth - 20
  }

  return {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    zIndex: 9999
  }
})

const arrowClass = computed(() => {
  if (!targetElement.value) return ''

  const rect = targetElement.value.getBoundingClientRect()
  const cardRect = {
    top: parseInt(cardStyle.value.top as string),
    left: parseInt(cardStyle.value.left as string)
  }

  if (cardRect.top > rect.bottom) {
    return 'arrow-up'
  } else {
    return 'arrow-down'
  }
})

// 方法
const nextStep = () => {
  if (currentStepIndex.value < props.steps.length - 1) {
    currentStep.value?.onExit?.()
    currentStepIndex.value++
    updateTargetElement()
    currentStep.value?.onEnter?.()
    emit('step-change', currentStep.value, currentStepIndex.value)
  }
}

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    currentStep.value?.onExit?.()
    currentStepIndex.value--
    updateTargetElement()
    currentStep.value?.onEnter?.()
    emit('step-change', currentStep.value, currentStepIndex.value)
  }
}

const finishGuide = () => {
  currentStep.value?.onExit?.()
  emit('finish')
  // 保存引导完成状态
  localStorage.setItem('user-guide-completed', 'true')
}

const skipGuide = () => {
  if (props.allowSkip) {
    currentStep.value?.onExit?.()
    emit('skip')
    localStorage.setItem('user-guide-skipped', 'true')
  }
}

const hideQuickTips = () => {
  localStorage.setItem('quick-tips-hidden', 'true')
}

const handleBackdropClick = () => {
  if (props.backdropDismiss) {
    skipGuide()
  }
}

const updateTargetElement = () => {
  if (currentStep.value?.target) {
    nextTick(() => {
      targetElement.value = document.querySelector(currentStep.value.target!)
    })
  } else {
    targetElement.value = null
  }
}

const startGuide = () => {
  currentStepIndex.value = 0
  updateTargetElement()
  currentStep.value?.onEnter?.()
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.visible) return

  switch (event.key) {
    case 'Escape':
      if (props.allowSkip) {
        skipGuide()
      }
      break
    case 'ArrowLeft':
      previousStep()
      break
    case 'ArrowRight':
      nextStep()
      break
    case 'Enter':
      if (isLastStep.value) {
        finishGuide()
      } else {
        nextStep()
      }
      break
  }
}

// 生命周期
onMounted(() => {
  if (props.autoStart && props.visible) {
    startGuide()
  }
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 监听步骤变化
watch(() => currentStepIndex.value, () => {
  updateTargetElement()
})

// 暴露方法给父组件
defineExpose({
  startGuide,
  nextStep,
  previousStep,
  finishGuide,
  skipGuide
})
</script>

<style scoped>
.user-guide-overlay {
  @apply fixed inset-0 z-50;
}

.guide-backdrop {
  @apply absolute inset-0;
}

.highlight-area {
  @apply bg-transparent border-2 border-blue-500 transition-all duration-300;
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0%, 100% { border-color: rgb(59 130 246); }
  50% { border-color: rgb(147 197 253); }
}

.guide-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 p-6 max-w-sm;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.guide-arrow {
  @apply absolute w-3 h-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 transform rotate-45;
}

.guide-arrow.arrow-up {
  @apply -top-2 left-1/2 -translate-x-1/2 border-b-0 border-r-0;
}

.guide-arrow.arrow-down {
  @apply -bottom-2 left-1/2 -translate-x-1/2 border-t-0 border-l-0;
}

.step-indicator {
  @apply flex items-center justify-between mb-4;
}

.step-dots {
  @apply flex space-x-2;
}

.step-dot {
  @apply w-2 h-2 rounded-full bg-gray-300 dark:bg-gray-600 transition-colors duration-200;
}

.step-dot.active {
  @apply bg-blue-500;
}

.step-dot.completed {
  @apply bg-green-500;
}

.step-counter {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.guide-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2;
}

.guide-description {
  @apply text-sm text-gray-600 dark:text-gray-400 mb-4;
}

.guide-media {
  @apply mb-4;
}

.guide-image {
  @apply w-full h-32 object-cover rounded-lg;
}

.guide-actions {
  @apply flex flex-wrap gap-2;
}

.guide-button {
  @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200 text-sm;
}

.guide-button-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white;
}

.guide-button-secondary {
  @apply bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300;
}

.guide-button-text {
  @apply text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300;
}

.quick-tips {
  @apply fixed bottom-4 right-4 z-50;
}

.quick-tips-content {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 p-4 max-w-xs;
}

.quick-tips-header {
  @apply flex items-center justify-between mb-3;
}

.quick-tips-header h4 {
  @apply text-sm font-medium text-gray-900 dark:text-gray-100;
}

.close-tips {
  @apply text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200;
}

.tips-list {
  @apply space-y-2;
}

.tip-item {
  @apply flex items-center space-x-2;
}

.tip-icon {
  @apply text-lg;
}

.tip-text {
  @apply text-xs text-gray-600 dark:text-gray-400;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .guide-card {
    @apply max-w-xs mx-4;
  }
  
  .quick-tips {
    @apply bottom-2 right-2;
  }
  
  .quick-tips-content {
    @apply max-w-xs;
  }
}
</style>
