<!--
  模型选择器组件
  提供AI模型选择、参数配置、快速切换等功能
-->
<template>
  <div class="model-selector-container">
    <!-- 模型选择按钮 -->
    <div class="relative">
      <button
        :disabled="disabled"
        class="model-button"
        :class="[
          'flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200',
          {
            'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300': !disabled,
            'bg-gray-50 dark:bg-gray-800 text-gray-400 cursor-not-allowed': disabled,
            'ring-2 ring-blue-500 ring-opacity-50': showDropdown
          }
        ]"
        @click="toggleDropdown"
      >
        <!-- 模型头像 -->
        <div class="flex-shrink-0">
          <img
            v-if="currentModel?.avatar"
            :src="currentModel.avatar"
            :alt="currentModel.name"
            class="w-5 h-5 rounded-full object-cover"
          />
          <div v-else class="w-5 h-5 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>

        <!-- 模型信息 -->
        <div class="flex-1 min-w-0 text-left">
          <div class="text-sm font-medium truncate">
            {{ currentModel?.name || '选择模型' }}
          </div>
          <div v-if="currentModel?.description" class="text-xs text-gray-500 dark:text-gray-400 truncate">
            {{ currentModel.description }}
          </div>
        </div>

        <!-- 下拉箭头 -->
        <div class="flex-shrink-0">
          <svg 
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': showDropdown }"
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </div>
      </button>

      <!-- 下拉菜单 -->
      <div
        v-if="showDropdown"
        class="dropdown-menu absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 z-50 max-h-80 overflow-y-auto"
      >
        <!-- 搜索框 -->
        <div class="p-3 border-b border-gray-200 dark:border-gray-600">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索模型..."
            class="w-full px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- 模型分类 -->
        <div class="p-2">
          <div v-for="category in filteredCategories" :key="category.name" class="mb-4 last:mb-0">
            <!-- 分类标题 -->
            <div class="px-2 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              {{ category.name }}
            </div>

            <!-- 模型列表 -->
            <div class="space-y-1">
              <button
                v-for="model in category.models"
                :key="model.id"
                class="model-option w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                :class="{
                  'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300': currentModel?.id === model.id,
                  'text-gray-700 dark:text-gray-300': currentModel?.id !== model.id
                }"
                @click="selectModel(model)"
              >
                <!-- 模型头像 -->
                <div class="flex-shrink-0">
                  <img
                    v-if="model.avatar"
                    :src="model.avatar"
                    :alt="model.name"
                    class="w-8 h-8 rounded-full object-cover"
                  />
                  <div v-else class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                </div>

                <!-- 模型信息 -->
                <div class="flex-1 min-w-0 text-left">
                  <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium truncate">{{ model.name }}</span>
                    <span v-if="model.isPro" class="px-1.5 py-0.5 text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded">
                      PRO
                    </span>
                    <span v-if="model.isNew" class="px-1.5 py-0.5 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">
                      NEW
                    </span>
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {{ model.description }}
                  </div>
                  <div v-if="model.capabilities" class="flex items-center space-x-1 mt-1">
                    <span v-for="capability in model.capabilities" :key="capability" class="px-1 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
                      {{ capability }}
                    </span>
                  </div>
                </div>

                <!-- 选中状态 -->
                <div v-if="currentModel?.id === model.id" class="flex-shrink-0">
                  <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredCategories.length === 0" class="p-8 text-center">
          <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.175-5.5-2.709"/>
          </svg>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            没有找到匹配的模型
          </div>
        </div>
      </div>
    </div>

    <!-- 快速切换按钮 -->
    <div v-if="showQuickSwitch && recentModels.length > 0" class="quick-switch mt-2">
      <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">最近使用</div>
      <div class="flex space-x-2">
        <button
          v-for="model in recentModels"
          :key="model.id"
          class="quick-switch-button flex items-center space-x-1 px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200"
          @click="selectModel(model)"
        >
          <img
            v-if="model.avatar"
            :src="model.avatar"
            :alt="model.name"
            class="w-4 h-4 rounded-full object-cover"
          />
          <span class="truncate max-w-20">{{ model.name }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'

interface ModelInfo {
  id: string
  name: string
  description: string
  avatar?: string
  category: string
  capabilities?: string[]
  isPro?: boolean
  isNew?: boolean
  parameters?: Record<string, any>
}

interface ModelCategory {
  name: string
  models: ModelInfo[]
}

interface Props {
  modelValue?: ModelInfo
  disabled?: boolean
  showQuickSwitch?: boolean
  models?: ModelInfo[]
}

interface Emits {
  (e: 'update:modelValue', model: ModelInfo): void
  (e: 'change', model: ModelInfo): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showQuickSwitch: true,
  models: () => []
})

const emit = defineEmits<Emits>()
const message = useMessage()

// 响应式数据
const showDropdown = ref(false)
const searchQuery = ref('')
const recentModels = ref<ModelInfo[]>([])

// 计算属性
const currentModel = computed(() => props.modelValue)

const modelCategories = computed<ModelCategory[]>(() => {
  const categories: Record<string, ModelInfo[]> = {}
  
  props.models.forEach(model => {
    if (!categories[model.category]) {
      categories[model.category] = []
    }
    categories[model.category].push(model)
  })

  return Object.entries(categories).map(([name, models]) => ({
    name,
    models: models.sort((a, b) => a.name.localeCompare(b.name))
  }))
})

const filteredCategories = computed(() => {
  if (!searchQuery.value.trim()) {
    return modelCategories.value
  }

  const query = searchQuery.value.toLowerCase()
  return modelCategories.value
    .map(category => ({
      ...category,
      models: category.models.filter(model =>
        model.name.toLowerCase().includes(query) ||
        model.description.toLowerCase().includes(query) ||
        model.capabilities?.some(cap => cap.toLowerCase().includes(query))
      )
    }))
    .filter(category => category.models.length > 0)
})

// 方法
const toggleDropdown = () => {
  if (!props.disabled) {
    showDropdown.value = !showDropdown.value
  }
}

const selectModel = (model: ModelInfo) => {
  emit('update:modelValue', model)
  emit('change', model)
  
  // 添加到最近使用
  addToRecentModels(model)
  
  // 关闭下拉菜单
  showDropdown.value = false
  searchQuery.value = ''
  
  message.success(`已切换到 ${model.name}`)
}

const addToRecentModels = (model: ModelInfo) => {
  // 移除已存在的相同模型
  recentModels.value = recentModels.value.filter(m => m.id !== model.id)
  
  // 添加到开头
  recentModels.value.unshift(model)
  
  // 限制最多5个
  if (recentModels.value.length > 5) {
    recentModels.value = recentModels.value.slice(0, 5)
  }
  
  // 保存到本地存储
  localStorage.setItem('recent-models', JSON.stringify(recentModels.value))
}

const loadRecentModels = () => {
  try {
    const saved = localStorage.getItem('recent-models')
    if (saved) {
      recentModels.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载最近使用的模型失败:', error)
  }
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as Element
  if (!target.closest('.model-selector-container')) {
    showDropdown.value = false
  }
}

// 生命周期
onMounted(() => {
  loadRecentModels()
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 暴露方法给父组件
defineExpose({
  toggleDropdown,
  selectModel
})
</script>

<style scoped>
.model-selector-container {
  @apply relative;
}

.model-button {
  @apply transition-all duration-200 ease-in-out;
  min-width: 200px;
}

.model-button:hover:not(:disabled) {
  @apply transform scale-[1.02];
}

.dropdown-menu {
  @apply animate-in slide-in-from-top-2 duration-200;
  min-width: 300px;
}

.model-option {
  @apply transition-all duration-200 ease-in-out;
}

.model-option:hover {
  @apply transform scale-[1.01];
}

.quick-switch-button {
  @apply transition-all duration-200 ease-in-out;
}

.quick-switch-button:hover {
  @apply transform scale-105;
}

/* 自定义滚动条 */
.dropdown-menu::-webkit-scrollbar {
  @apply w-2;
}

.dropdown-menu::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700 rounded;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-500 rounded;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-400;
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .dropdown-menu {
    @apply bg-gray-800 border-gray-600;
  }
}
</style>
