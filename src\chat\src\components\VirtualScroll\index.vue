<!--
  虚拟滚动组件
  用于优化大量消息的渲染性能
-->
<template>
  <div
    ref="containerRef"
    class="virtual-scroll-container"
    :style="{ height: containerHeight + 'px' }"
    @scroll="handleScroll"
  >
    <!-- 上方占位 -->
    <div :style="{ height: offsetY + 'px' }"></div>

    <!-- 可见项目 -->
    <div class="visible-items">
      <slot
        v-for="(item, index) in visibleItems"
        :key="getItemKey(item, startIndex + index)"
        :item="item"
        :index="startIndex + index"
        name="item"
      />
    </div>

    <!-- 下方占位 -->
    <div :style="{ height: (totalHeight - offsetY - visibleHeight) + 'px' }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

interface Props {
  items: any[]
  itemHeight?: number
  containerHeight?: number
  buffer?: number
  keyField?: string
}

interface Emits {
  (e: 'scroll', event: Event): void
  (e: 'reach-bottom'): void
  (e: 'reach-top'): void
}

const props = withDefaults(defineProps<Props>(), {
  itemHeight: 100,
  containerHeight: 400,
  buffer: 5,
  keyField: 'id'
})

const emit = defineEmits<Emits>()

// 响应式数据
const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)
const itemHeights = ref<Map<number, number>>(new Map())

// 计算属性
const totalHeight = computed(() => {
  if (itemHeights.value.size === 0) {
    return props.items.length * props.itemHeight
  }

  let height = 0
  for (let i = 0; i < props.items.length; i++) {
    height += itemHeights.value.get(i) || props.itemHeight
  }
  return height
})

const startIndex = computed(() => {
  let accumulatedHeight = 0
  let index = 0

  while (index < props.items.length && accumulatedHeight < scrollTop.value) {
    accumulatedHeight += itemHeights.value.get(index) || props.itemHeight
    index++
  }

  return Math.max(0, index - props.buffer)
})

const endIndex = computed(() => {
  let accumulatedHeight = 0
  let index = startIndex.value

  while (index < props.items.length && accumulatedHeight < props.containerHeight + props.itemHeight * props.buffer) {
    accumulatedHeight += itemHeights.value.get(index) || props.itemHeight
    index++
  }

  return Math.min(props.items.length - 1, index + props.buffer)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value + 1)
})

const offsetY = computed(() => {
  let height = 0
  for (let i = 0; i < startIndex.value; i++) {
    height += itemHeights.value.get(i) || props.itemHeight
  }
  return height
})

const visibleHeight = computed(() => {
  let height = 0
  for (let i = startIndex.value; i <= endIndex.value; i++) {
    height += itemHeights.value.get(i) || props.itemHeight
  }
  return height
})

// 方法
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop

  emit('scroll', event)

  // 检查是否到达底部
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
    emit('reach-bottom')
  }

  // 检查是否到达顶部
  if (target.scrollTop <= 10) {
    emit('reach-top')
  }
}

const getItemKey = (item: any, index: number) => {
  if (props.keyField && item[props.keyField]) {
    return item[props.keyField]
  }
  return index
}

const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = containerRef.value.scrollHeight
  }
}

const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

const scrollToIndex = (index: number) => {
  if (!containerRef.value) return

  let height = 0
  for (let i = 0; i < index; i++) {
    height += itemHeights.value.get(i) || props.itemHeight
  }

  containerRef.value.scrollTop = height
}

// 更新项目高度
const updateItemHeight = (index: number, height: number) => {
  itemHeights.value.set(index, height)
}

// 观察项目高度变化
const observeItemHeights = () => {
  if (!containerRef.value) return

  const resizeObserver = new ResizeObserver((entries) => {
    entries.forEach((entry) => {
      const element = entry.target as HTMLElement
      const index = parseInt(element.dataset.index || '0')
      const height = entry.contentRect.height

      if (height > 0) {
        updateItemHeight(index, height)
      }
    })
  })

  // 观察所有可见项目
  const items = containerRef.value.querySelectorAll('[data-index]')
  items.forEach((item) => {
    resizeObserver.observe(item)
  })

  return resizeObserver
}

// 生命周期
let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  nextTick(() => {
    resizeObserver = observeItemHeights()
  })
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 监听可见项目变化，重新观察高度
watch(visibleItems, () => {
  nextTick(() => {
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
    resizeObserver = observeItemHeights()
  })
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  scrollToTop,
  scrollToIndex,
  updateItemHeight
})
</script>

<style scoped>
.virtual-scroll-container {
  @apply overflow-auto;
}

.visible-items {
  @apply space-y-2;
}

/* 自定义滚动条 */
.virtual-scroll-container::-webkit-scrollbar {
  @apply w-2;
}

.virtual-scroll-container::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700 rounded;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-500 rounded;
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-400;
}

/* 平滑滚动 */
.virtual-scroll-container {
  scroll-behavior: smooth;
}

/* 性能优化 */
.virtual-scroll-container {
  contain: layout style paint;
  will-change: scroll-position;
}

.visible-items {
  contain: layout style;
}
</style>
