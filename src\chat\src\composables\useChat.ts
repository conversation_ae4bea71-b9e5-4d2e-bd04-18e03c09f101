/**
 * 统一的聊天状态管理钩子
 * 整合所有聊天相关的状态和操作，提供类型安全的API
 */

import { ref, computed, reactive, nextTick, readonly, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { useChatStore, useAuthStore, useGlobalStoreWithOut } from '@/store'
import { fetchChatAPIProcess } from '@/api'
import type {
  ChatState,
  UserMessage,
  AIMessage,
  SendMessageOptions,
  UseChatReturn,
  ErrorType,
  MessageStatus,
  StreamData
} from '@/types/chat'

export function useChat(): UseChatReturn {
  const message = useMessage()
  const chatStore = useChatStore()
  const authStore = useAuthStore()
  const globalStore = useGlobalStoreWithOut()

  // 响应式状态
  const state = reactive<ChatState>({
    activeGroupId: computed(() => chatStore.active),
    groups: computed(() => chatStore.groupList),
    messages: computed(() => chatStore.chatList),
    input: {
      text: computed({
        get: () => chatStore.prompt,
        set: (value) => chatStore.setPrompt(value)
      }),
      isComposing: false,
      isUploading: false,
      uploadProgress: 0,
      selectedFiles: [],
      selectedApp: undefined,
      showSuggestions: false,
      suggestions: []
    },
    ui: {
      isStreamIn: computed(() => chatStore.isStreamIn || false),
      isLoading: false,
      showWelcome: computed(() => state.messages.length === 0),
      showCodePreview: false,
      sidebarCollapsed: computed(() => globalStore.siderCollapsed),
      currentTheme: 'auto',
      isMobile: false
    },
    error: {
      hasError: false,
      errorType: undefined,
      errorMessage: undefined,
      canRetry: false,
      retryCount: 0
    },
    modelConfig: computed(() => {
      const activeGroup = chatStore.getChatByGroupInfo()
      if (!activeGroup?.config) return undefined

      try {
        const config = JSON.parse(activeGroup.config)
        return config.modelInfo
      } catch {
        return undefined
      }
    }),
    currentPlugin: computed(() => chatStore.currentPlugin)
  })

  // 控制器用于取消请求
  const abortController = ref<AbortController | null>(null)

  /**
   * 发送消息
   */
  const sendMessage = async (text: string, options: SendMessageOptions = {}) => {
    if (!text.trim() || state.ui.isStreamIn) {
      return
    }

    try {
      // 清除之前的错误状态
      clearError()

      // 如果没有聊天组，创建新的
      if (state.groups.length === 0) {
        await createNewChatGroup()
      }

      // 设置流式输入状态
      chatStore.setStreamIn(true)
      state.ui.isLoading = true

      // 获取模型配置
      const modelConfig = state.modelConfig
      if (!modelConfig) {
        throw new Error('模型配置未找到')
      }

      // 添加用户消息
      const userMessage: UserMessage = {
        id: generateMessageId(),
        text,
        timestamp: Date.now(),
        inversion: true,
        fileInfo: options.fileUrl ? {
          fileName: '上传文件',
          fileSize: 0,
          fileType: 'unknown',
          fileUrl: options.fileUrl
        } : undefined
      }

      chatStore.addGroupChat({
        text,
        inversion: true,
        error: false,
        conversationOptions: null,
        requestOptions: { prompt: text, options: null }
      })

      // 添加AI消息占位符
      const aiMessage: Partial<AIMessage> = {
        text: '',
        model: options.model || modelConfig.model,
        modelName: modelConfig.modelName,
        modelType: modelConfig.modelType,
        modelAvatar: modelConfig.modelAvatar,
        loading: true,
        inversion: false,
        error: false,
        status: MessageStatus.PROCESSING,
        pluginParam: options.pluginParam || state.currentPlugin?.parameters
      }

      chatStore.addGroupChat(aiMessage)

      // 清空输入
      await chatStore.setPrompt('')
      state.input.selectedFiles = []

      // 创建新的控制器
      abortController.value = new AbortController()

      // 发送API请求
      await handleStreamResponse(text, options, abortController.value)

    } catch (error) {
      console.error('发送消息失败:', error)
      handleError(error)
    } finally {
      chatStore.setStreamIn(false)
      state.ui.isLoading = false
      globalStore.updateIsChatIn(false)
    }
  }

  /**
   * 处理流式响应
   */
  const handleStreamResponse = async (
    text: string,
    options: SendMessageOptions,
    controller: AbortController
  ) => {
    const modelConfig = state.modelConfig!
    let responseText = ''

    const onMessage = (data: StreamData) => {
      if (data.text) {
        responseText += data.text
      }

      // 更新最后一条AI消息
      const lastIndex = state.messages.length - 1
      chatStore.updateGroupChatSome(lastIndex, {
        text: responseText,
        loading: data.status !== MessageStatus.COMPLETED,
        status: data.status || MessageStatus.PROCESSING,
        chatId: data.chatId,
        ttsUrl: data.ttsUrl,
        videoUrl: data.videoUrl,
        audioUrl: data.audioUrl,
        taskData: data.taskData,
        fileInfo: data.fileInfo
      })
    }

    const onError = (error: any) => {
      console.error('流式响应错误:', error)

      // 更新最后一条消息为错误状态
      const lastIndex = state.messages.length - 1
      chatStore.updateGroupChatSome(lastIndex, {
        loading: false,
        error: true,
        status: MessageStatus.FAILED,
        text: responseText || '抱歉，处理您的请求时出现了错误。'
      })

      handleError(error)
    }

    // 调用API
    await fetchChatAPIProcess({
      prompt: text,
      options: {
        conversationId: state.activeGroupId,
        parentMessageId: null
      },
      signal: controller.signal,
      onMessage,
      onError
    })
  }

  /**
   * 重新生成消息
   */
  const regenerateMessage = async (index: number) => {
    if (index < 0 || index >= state.messages.length) {
      return
    }

    const message = state.messages[index]
    if (message.inversion) {
      // 如果是用户消息，重新发送
      await sendMessage(message.text)
    } else {
      // 如果是AI消息，找到对应的用户消息重新发送
      const userMessage = state.messages[index - 1]
      if (userMessage && userMessage.inversion) {
        // 删除当前AI消息
        deleteMessage(index)
        // 重新发送用户消息
        await sendMessage(userMessage.text)
      }
    }
  }

  /**
   * 删除消息
   */
  const deleteMessage = (index: number) => {
    if (index < 0 || index >= state.messages.length) {
      return
    }

    chatStore.deleteGroupChat(index)
    message.success('消息已删除')
  }

  /**
   * 更新输入内容
   */
  const updateInput = (text: string) => {
    chatStore.setPrompt(text)
  }

  /**
   * 上传文件
   */
  const uploadFiles = async (files: File[]) => {
    if (!files.length) return

    state.input.isUploading = true
    state.input.uploadProgress = 0

    try {
      // 这里应该调用文件上传API
      // const uploadResults = await uploadFilesAPI(files)

      state.input.selectedFiles = files
      message.success(`已选择 ${files.length} 个文件`)
    } catch (error) {
      console.error('文件上传失败:', error)
      handleError(error, 'upload')
    } finally {
      state.input.isUploading = false
      state.input.uploadProgress = 0
    }
  }

  /**
   * 重试最后一条消息
   */
  const retryLastMessage = async () => {
    const lastMessage = state.messages[state.messages.length - 1]
    if (!lastMessage || lastMessage.inversion) {
      return
    }

    // 找到对应的用户消息
    const userMessage = state.messages[state.messages.length - 2]
    if (userMessage && userMessage.inversion) {
      // 删除失败的AI消息
      deleteMessage(state.messages.length - 1)
      // 重新发送
      await sendMessage(userMessage.text)
    }
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    state.error = {
      hasError: false,
      errorType: undefined,
      errorMessage: undefined,
      canRetry: false,
      retryCount: 0
    }
  }

  /**
   * 处理错误
   */
  const handleError = (error: any, type?: ErrorType) => {
    let errorType: ErrorType = type || 'unknown'
    let errorMessage = '发生未知错误'
    let canRetry = false

    if (error.code === 401) {
      errorType = 'auth'
      errorMessage = '认证失败，请重新登录'
    } else if (error.code === 402 || error.message?.includes('余额不足')) {
      errorType = 'validation'
      errorMessage = '余额不足，请充值后继续使用'
    } else if (error.code >= 500) {
      errorType = 'server'
      errorMessage = '服务器错误，请稍后重试'
      canRetry = true
    } else if (error.name === 'NetworkError') {
      errorType = 'network'
      errorMessage = '网络连接失败，请检查网络后重试'
      canRetry = true
    }

    state.error = {
      hasError: true,
      errorType,
      errorMessage,
      canRetry,
      retryCount: state.error.retryCount + 1
    }

    message.error(errorMessage)
  }

  /**
   * 创建新的聊天组
   */
  const createNewChatGroup = async () => {
    await chatStore.addGroupChat({
      title: '新对话',
      isEdit: false
    })
  }

  /**
   * 生成消息ID
   */
  const generateMessageId = () => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 停止当前请求
   */
  const stopCurrentRequest = () => {
    if (abortController.value) {
      abortController.value.abort()
      abortController.value = null
    }
    chatStore.setStreamIn(false)
    state.ui.isLoading = false
  }

  return {
    state: readonly(ref(state)),
    sendMessage,
    regenerateMessage,
    deleteMessage,
    updateInput,
    uploadFiles,
    retryLastMessage,
    clearError,
    stopCurrentRequest
  }
}
