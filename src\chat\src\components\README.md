# 聊天组件库

这是经过全面重构和优化的聊天组件库，提供了模块化、类型安全、高性能的聊天功能组件。

## 🏗️ 架构概览

```
src/components/
├── ChatInput/           # 聊天输入组件模块
│   ├── index.vue       # 主入口组件
│   ├── InputArea.vue   # 输入区域
│   ├── FileUpload.vue  # 文件上传
│   └── ModelSelector.vue # 模型选择器
├── WelcomeScreen/      # 欢迎界面组件
│   └── index.vue
├── ErrorHandler/       # 错误处理组件
│   └── index.vue
├── VirtualScroll/      # 虚拟滚动组件
│   └── index.vue
└── UserGuide/          # 用户引导组件
    └── index.vue
```

## 📦 核心组件

### ChatInput - 统一聊天输入组件

主要的聊天输入组件，整合了所有输入相关功能。

#### 基本用法

```vue
<template>
  <ChatInput
    v-model="inputText"
    :disabled="isLoading"
    :is-loading="isLoading"
    :selected-model="currentModel"
    :available-models="models"
    :show-model-selector="true"
    :allow-file-upload="true"
    @send="handleSend"
    @model-change="handleModelChange"
    @file-upload="handleFileUpload"
  />
</template>

<script setup>
import ChatInput from '@/components/ChatInput/index.vue'

const inputText = ref('')
const isLoading = ref(false)
const currentModel = ref(null)
const models = ref([...])

const handleSend = (data) => {
  console.log('发送消息:', data.text)
  console.log('选择的模型:', data.model)
  console.log('上传的文件:', data.files)
}
</script>
```

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `modelValue` | `string` | `''` | 输入文本（v-model） |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `isLoading` | `boolean` | `false` | 是否加载中 |
| `hasError` | `boolean` | `false` | 是否有错误 |
| `canRetry` | `boolean` | `false` | 是否可重试 |
| `canStop` | `boolean` | `true` | 是否可停止 |
| `selectedModel` | `ModelInfo` | `undefined` | 当前选择的模型 |
| `availableModels` | `ModelInfo[]` | `[]` | 可用模型列表 |
| `showModelSelector` | `boolean` | `false` | 是否显示模型选择器 |
| `allowFileUpload` | `boolean` | `true` | 是否允许文件上传 |
| `allowVoiceInput` | `boolean` | `false` | 是否允许语音输入 |
| `quickPrompts` | `QuickPrompt[]` | `[]` | 快捷提示列表 |

#### Events

| 事件 | 参数 | 描述 |
|------|------|------|
| `send` | `{ text: string, model?: ModelInfo, files?: File[] }` | 发送消息 |
| `stop` | - | 停止当前操作 |
| `retry` | - | 重试操作 |
| `clear` | - | 清空输入 |
| `model-change` | `ModelInfo` | 模型变更 |
| `file-upload` | `File[]` | 文件上传 |

### InputArea - 输入区域组件

核心的文本输入组件，支持自动高度调整、快捷键等功能。

#### 基本用法

```vue
<template>
  <InputArea
    v-model="text"
    placeholder="输入您的问题..."
    :max-length="4000"
    :auto-focus="true"
    @send="handleSend"
  />
</template>
```

#### 特性

- ✅ 自动高度调整
- ✅ 快捷键支持（Enter发送，Shift+Enter换行）
- ✅ 字符计数显示
- ✅ 输入状态提示
- ✅ 移动端优化

### FileUpload - 文件上传组件

支持拖拽上传、多文件选择、进度显示的文件上传组件。

#### 基本用法

```vue
<template>
  <FileUpload
    :max-size="10 * 1024 * 1024"
    :max-files="5"
    :allowed-extensions="['jpg', 'png', 'pdf', 'txt']"
    @upload="handleUpload"
    @error="handleError"
  />
</template>
```

#### 特性

- ✅ 拖拽上传支持
- ✅ 文件类型验证
- ✅ 文件大小限制
- ✅ 上传进度显示
- ✅ 文件预览
- ✅ 错误处理

### ModelSelector - 模型选择器

智能的AI模型选择组件，支持搜索、分类、最近使用等功能。

#### 基本用法

```vue
<template>
  <ModelSelector
    v-model="selectedModel"
    :models="availableModels"
    :show-quick-switch="true"
    @change="handleModelChange"
  />
</template>
```

#### 特性

- ✅ 模型搜索和过滤
- ✅ 分类显示
- ✅ 最近使用记录
- ✅ 模型能力标签
- ✅ 响应式设计

### WelcomeScreen - 欢迎界面

统一的欢迎界面组件，支持不同模式和智能引导。

#### 基本用法

```vue
<template>
  <WelcomeScreen
    mode="teacher"
    :app-info="appInfo"
    @use-prompt="handlePrompt"
    @feature-click="handleFeature"
  />
</template>
```

#### 模式

- `teacher` - 教师模式
- `student` - 学生模式  
- `app` - 应用模式
- `default` - 默认模式

### ErrorHandler - 错误处理

统一的错误处理组件，提供友好的错误提示和恢复机制。

#### 基本用法

```vue
<template>
  <ErrorHandler
    :visible="hasError"
    :error-type="errorType"
    :error-message="errorMessage"
    :can-retry="true"
    :auto-retry="true"
    @retry="handleRetry"
    @dismiss="clearError"
  />
</template>
```

#### 错误类型

- `network` - 网络错误
- `auth` - 认证错误
- `validation` - 验证错误
- `server` - 服务器错误
- `upload` - 上传错误
- `unknown` - 未知错误

## 🎯 状态管理

### useChat Hook

统一的聊天状态管理钩子，提供类型安全的API。

```typescript
import { useChat } from '@/composables/useChat'

const {
  state,
  sendMessage,
  regenerateMessage,
  deleteMessage,
  updateInput,
  uploadFiles,
  retryLastMessage,
  clearError
} = useChat()

// 发送消息
await sendMessage('你好', {
  model: 'gpt-4',
  fileUrl: 'uploaded-file-url'
})

// 访问状态
console.log(state.value.messages)
console.log(state.value.ui.isLoading)
console.log(state.value.error.hasError)
```

## 🎨 样式系统

### 设计原则

1. **一致性** - 统一的设计语言和视觉风格
2. **响应式** - 完美适配桌面端和移动端
3. **可访问性** - 支持键盘导航和屏幕阅读器
4. **性能** - 优化动画和过渡效果

### 主题支持

组件支持明暗主题自动切换：

```css
/* 明亮主题 */
.light-theme {
  --bg-primary: #ffffff;
  --text-primary: #1f2937;
  --border-color: #e5e7eb;
}

/* 暗黑主题 */
.dark-theme {
  --bg-primary: #1f2937;
  --text-primary: #f9fafb;
  --border-color: #374151;
}
```

## 📱 移动端优化

### 触摸手势

使用 `useTouch` 钩子处理移动端交互：

```typescript
import { useTouch } from '@/composables/useTouch'

const { onSwipe, onTap, onLongPress, bindElement } = useTouch({
  threshold: 50,
  maxDuration: 500
})

onSwipe((swipe) => {
  if (swipe.direction === 'left') {
    // 处理左滑
  }
})
```

### 响应式断点

```css
/* 移动端 */
@media (max-width: 768px) {
  .chat-input {
    padding: 0.5rem;
  }
}

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) {
  .chat-input {
    padding: 1rem;
  }
}
```

## 🚀 性能优化

### 虚拟滚动

对于大量消息，使用 `VirtualScroll` 组件：

```vue
<template>
  <VirtualScroll
    :items="messages"
    :item-height="100"
    :container-height="400"
    @reach-bottom="loadMore"
  >
    <template #item="{ item, index }">
      <MessageItem :message="item" :index="index" />
    </template>
  </VirtualScroll>
</template>
```

### 懒加载

图片和文件预览支持懒加载：

```vue
<template>
  <img
    v-lazy="imageUrl"
    :alt="imageAlt"
    class="lazy-image"
  />
</template>
```

## 🧪 测试

### 单元测试

```bash
# 运行所有测试
npm run test

# 运行特定组件测试
npm run test ChatInput

# 生成覆盖率报告
npm run test:coverage
```

### 测试示例

```typescript
import { mount } from '@vue/test-utils'
import ChatInput from '@/components/ChatInput/index.vue'

describe('ChatInput', () => {
  it('应该正确处理发送事件', async () => {
    const wrapper = mount(ChatInput)
    
    await wrapper.setProps({ modelValue: '测试消息' })
    await wrapper.find('.send-button').trigger('click')
    
    expect(wrapper.emitted('send')).toBeTruthy()
  })
})
```

## 📚 最佳实践

### 1. 组件使用

- 优先使用统一的 `ChatInput` 组件
- 根据需要启用/禁用特定功能
- 正确处理错误状态和加载状态

### 2. 状态管理

- 使用 `useChat` 钩子管理聊天状态
- 避免直接操作 store，通过钩子提供的方法
- 正确处理异步操作和错误

### 3. 性能优化

- 大量消息时使用虚拟滚动
- 合理使用懒加载和防抖
- 避免不必要的重渲染

### 4. 可访问性

- 提供合适的 ARIA 标签
- 支持键盘导航
- 确保颜色对比度符合标准

## 🔧 开发指南

### 添加新功能

1. 在对应的组件目录下创建新文件
2. 更新类型定义 (`src/types/chat.ts`)
3. 添加相应的测试用例
4. 更新文档

### 调试技巧

```typescript
// 启用调试模式
const DEBUG = process.env.NODE_ENV === 'development'

if (DEBUG) {
  console.log('聊天状态:', state.value)
}
```

### 性能监控

```typescript
// 性能监控
const startTime = performance.now()
await sendMessage(text)
const endTime = performance.now()
console.log(`发送消息耗时: ${endTime - startTime}ms`)
```

## 📄 更新日志

### v2.0.0 (2024-01-XX)

- 🎉 全面重构组件架构
- ✨ 新增统一的 ChatInput 组件
- 🚀 性能优化和虚拟滚动
- 📱 完整的移动端支持
- 🛡️ 完善的 TypeScript 类型
- 🧪 全面的测试覆盖
- 📚 详细的文档和示例

### v1.x.x

- 基础聊天功能
- 简单的组件实现
