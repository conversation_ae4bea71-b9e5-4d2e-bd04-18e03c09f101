<!--
  统一的错误处理组件
  提供友好的错误提示、重试机制和错误恢复功能
-->
<template>
  <div v-if="visible" class="error-handler">
    <!-- 错误提示卡片 -->
    <div 
      class="error-card"
      :class="[
        'relative p-4 rounded-lg border transition-all duration-300',
        errorTypeClasses
      ]"
    >
      <!-- 错误图标和标题 -->
      <div class="error-header flex items-start space-x-3">
        <div class="error-icon flex-shrink-0">
          <component :is="errorIcon" class="w-6 h-6" />
        </div>
        
        <div class="error-content flex-1">
          <h3 class="error-title text-lg font-semibold mb-1">
            {{ errorTitle }}
          </h3>
          <p class="error-message text-sm opacity-90">
            {{ displayMessage }}
          </p>
          
          <!-- 错误详情 (可展开) -->
          <div v-if="showDetails && errorDetails" class="error-details mt-3">
            <button
              class="details-toggle text-sm underline opacity-75 hover:opacity-100 transition-opacity duration-200"
              @click="toggleDetails"
            >
              {{ detailsExpanded ? '隐藏详情' : '查看详情' }}
            </button>
            
            <div v-if="detailsExpanded" class="details-content mt-2 p-3 bg-black bg-opacity-10 rounded text-xs font-mono">
              {{ errorDetails }}
            </div>
          </div>
        </div>
        
        <!-- 关闭按钮 -->
        <button
          v-if="dismissible"
          class="close-button flex-shrink-0 p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors duration-200"
          @click="handleDismiss"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
      
      <!-- 操作按钮 -->
      <div v-if="showActions" class="error-actions mt-4 flex items-center space-x-3">
        <!-- 重试按钮 -->
        <button
          v-if="canRetry"
          :disabled="isRetrying"
          class="retry-button"
          :class="[
            'flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200',
            retryButtonClasses
          ]"
          @click="handleRetry"
        >
          <svg v-if="!isRetrying" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          <div v-else class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
          <span>{{ isRetrying ? '重试中...' : '重试' }}</span>
        </button>
        
        <!-- 刷新页面按钮 -->
        <button
          v-if="showRefresh"
          class="refresh-button flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors duration-200"
          @click="handleRefresh"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          <span>刷新页面</span>
        </button>
        
        <!-- 联系支持按钮 -->
        <button
          v-if="showSupport"
          class="support-button flex items-center space-x-2 px-4 py-2 bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-lg font-medium transition-colors duration-200"
          @click="handleSupport"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <span>联系支持</span>
        </button>
      </div>
      
      <!-- 重试计数 -->
      <div v-if="retryCount > 0" class="retry-count mt-2 text-xs opacity-75">
        已重试 {{ retryCount }} 次
      </div>
    </div>
    
    <!-- 网络状态指示器 -->
    <div v-if="errorType === 'network'" class="network-status mt-3">
      <div class="flex items-center space-x-2 text-sm">
        <div 
          class="network-indicator w-2 h-2 rounded-full"
          :class="{
            'bg-red-500': !isOnline,
            'bg-green-500': isOnline,
            'bg-yellow-500': isReconnecting
          }"
        ></div>
        <span class="text-gray-600 dark:text-gray-400">
          {{ networkStatusText }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'

type ErrorType = 'network' | 'auth' | 'validation' | 'server' | 'upload' | 'unknown'

interface Props {
  visible?: boolean
  errorType?: ErrorType
  errorMessage?: string
  errorDetails?: string
  canRetry?: boolean
  retryCount?: number
  dismissible?: boolean
  showActions?: boolean
  showDetails?: boolean
  showRefresh?: boolean
  showSupport?: boolean
  autoRetry?: boolean
  autoRetryDelay?: number
  maxAutoRetries?: number
}

interface Emits {
  (e: 'retry'): void
  (e: 'dismiss'): void
  (e: 'refresh'): void
  (e: 'support'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  errorType: 'unknown',
  errorMessage: '',
  errorDetails: '',
  canRetry: false,
  retryCount: 0,
  dismissible: true,
  showActions: true,
  showDetails: false,
  showRefresh: false,
  showSupport: false,
  autoRetry: false,
  autoRetryDelay: 3000,
  maxAutoRetries: 3
})

const emit = defineEmits<Emits>()
const message = useMessage()

// 响应式数据
const isRetrying = ref(false)
const detailsExpanded = ref(false)
const isOnline = ref(navigator.onLine)
const isReconnecting = ref(false)
const autoRetryTimer = ref<number | null>(null)

// 计算属性
const errorTypeClasses = computed(() => {
  const classes = {
    network: 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800 text-orange-800 dark:text-orange-200',
    auth: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200',
    validation: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200',
    server: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200',
    upload: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200',
    unknown: 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800 text-gray-800 dark:text-gray-200'
  }
  return classes[props.errorType]
})

const retryButtonClasses = computed(() => {
  const classes = {
    network: 'bg-orange-600 hover:bg-orange-700 text-white',
    auth: 'bg-red-600 hover:bg-red-700 text-white',
    validation: 'bg-yellow-600 hover:bg-yellow-700 text-white',
    server: 'bg-red-600 hover:bg-red-700 text-white',
    upload: 'bg-blue-600 hover:bg-blue-700 text-white',
    unknown: 'bg-gray-600 hover:bg-gray-700 text-white'
  }
  return classes[props.errorType]
})

const errorIcon = computed(() => {
  const icons = {
    network: 'WifiOffIcon',
    auth: 'LockClosedIcon',
    validation: 'ExclamationTriangleIcon',
    server: 'ServerIcon',
    upload: 'CloudArrowUpIcon',
    unknown: 'ExclamationCircleIcon'
  }
  return icons[props.errorType]
})

const errorTitle = computed(() => {
  const titles = {
    network: '网络连接失败',
    auth: '认证失败',
    validation: '输入验证失败',
    server: '服务器错误',
    upload: '文件上传失败',
    unknown: '发生错误'
  }
  return titles[props.errorType]
})

const displayMessage = computed(() => {
  if (props.errorMessage) {
    return props.errorMessage
  }
  
  const defaultMessages = {
    network: '请检查您的网络连接后重试',
    auth: '登录已过期，请重新登录',
    validation: '请检查输入内容是否正确',
    server: '服务器暂时不可用，请稍后重试',
    upload: '文件上传失败，请重新尝试',
    unknown: '发生未知错误，请重试'
  }
  return defaultMessages[props.errorType]
})

const networkStatusText = computed(() => {
  if (!isOnline.value) {
    return '网络已断开'
  }
  if (isReconnecting.value) {
    return '正在重新连接...'
  }
  return '网络已连接'
})

// 方法
const handleRetry = async () => {
  if (isRetrying.value) return
  
  isRetrying.value = true
  
  try {
    emit('retry')
    message.info('正在重试...')
  } finally {
    setTimeout(() => {
      isRetrying.value = false
    }, 1000)
  }
}

const handleDismiss = () => {
  emit('dismiss')
}

const handleRefresh = () => {
  emit('refresh')
  window.location.reload()
}

const handleSupport = () => {
  emit('support')
  // 可以打开支持页面或显示联系方式
  message.info('正在跳转到支持页面...')
}

const toggleDetails = () => {
  detailsExpanded.value = !detailsExpanded.value
}

const handleOnlineStatusChange = () => {
  isOnline.value = navigator.onLine
  
  if (isOnline.value && props.errorType === 'network') {
    isReconnecting.value = true
    setTimeout(() => {
      isReconnecting.value = false
      if (props.autoRetry && props.retryCount < props.maxAutoRetries) {
        handleRetry()
      }
    }, 1000)
  }
}

const startAutoRetry = () => {
  if (!props.autoRetry || props.retryCount >= props.maxAutoRetries) {
    return
  }
  
  autoRetryTimer.value = window.setTimeout(() => {
    if (props.canRetry) {
      handleRetry()
    }
  }, props.autoRetryDelay)
}

const stopAutoRetry = () => {
  if (autoRetryTimer.value) {
    clearTimeout(autoRetryTimer.value)
    autoRetryTimer.value = null
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('online', handleOnlineStatusChange)
  window.addEventListener('offline', handleOnlineStatusChange)
  
  if (props.visible && props.autoRetry) {
    startAutoRetry()
  }
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnlineStatusChange)
  window.removeEventListener('offline', handleOnlineStatusChange)
  stopAutoRetry()
})

// 监听可见性变化
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.autoRetry) {
    startAutoRetry()
  } else {
    stopAutoRetry()
  }
})

// 暴露方法给父组件
defineExpose({
  retry: handleRetry,
  dismiss: handleDismiss
})
</script>

<style scoped>
.error-handler {
  @apply animate-in slide-in-from-top-2 duration-300;
}

.error-card {
  @apply shadow-md;
}

.error-icon {
  @apply mt-0.5;
}

.details-content {
  @apply max-h-32 overflow-y-auto;
}

.details-content::-webkit-scrollbar {
  @apply w-1;
}

.details-content::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.details-content::-webkit-scrollbar-thumb {
  @apply bg-current opacity-30 rounded;
}

.retry-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.network-indicator {
  @apply animate-pulse;
}

/* 动画效果 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.error-card.shake {
  animation: shake 0.5s ease-in-out;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .error-actions {
    @apply flex-col space-y-2 space-x-0;
  }
  
  .retry-button,
  .refresh-button,
  .support-button {
    @apply w-full justify-center;
  }
}
</style>
