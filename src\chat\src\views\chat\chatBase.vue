<script setup lang="ts">
import { fetchChatAPIProcess } from '@/api';
import { useBasicLayout } from '@/hooks/useBasicLayout';
import { t } from '@/locales';
import { router } from '@/router';
import { useAuthStore, useChatStore, useGlobalStoreWithOut } from '@/store';
import { DownSmall } from '@icon-park/vue-next';
import { useDialog, useMessage } from 'naive-ui';
import {
  computed,
  inject,
  nextTick,
  onMounted,
  onUnmounted,
  provide,
  ref,
  watch,
} from 'vue';
import { useRoute } from 'vue-router';
import { Message } from './components';
import ChatToolbar from './components/ChatToolbar.vue';
// 新的组件架构
import ChatInput from '../../components/ChatInput/index.vue';
import WelcomeScreen from '../../components/WelcomeScreen/index.vue';
import ErrorHandler from '../../components/ErrorHandler/index.vue';
import { useChat } from '../../composables/useChat';
import { useCopyCode } from './hooks/useCopyCode';
import { useScroll } from './hooks/useScroll';
import { useModelInfo } from '@/composables/useModelInfo';
import type { ModelInfo, AppInfo } from '../../types/chat';

// 引入的依赖
const route = useRoute();
const dialog = useDialog();
const ms = useMessage();
const { isMobile } = useBasicLayout();
const { scrollRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll();
useCopyCode();

// 引用的 store
const useGlobalStore = useGlobalStoreWithOut();
const authStore = useAuthStore();
const chatStore = useChatStore();

// 使用新的聊天钩子
const {
  state: chatState,
  sendMessage,
  regenerateMessage,
  deleteMessage,
  updateInput,
  uploadFiles,
  retryLastMessage,
  clearError,
  stopCurrentRequest
} = useChat();

// 新增的响应式数据
const availableModels = ref<ModelInfo[]>([]);
const quickPrompts = ref([
  { id: '1', label: '写作助手', text: '帮我写一篇关于人工智能的文章' },
  { id: '2', label: '代码助手', text: '帮我写一个Python函数' },
  { id: '3', label: '学习助手', text: '解释一下这个概念' }
]);
const appSuggestions = ref<AppInfo[]>([]);
const showAppSuggestions = ref(false);

// 使用模型信息管理
const {
  activeGroupInfo,
  activeModel,
  activeModelName,
  activeModelAvatar,
  activeModelKeyType,
  fileParsing
} = useModelInfo();

// ref 和计算属性
const bottomContainer = ref();
const firstScroll = ref<boolean>(true);
const isAtBottom = ref(false);
const controller = ref(new AbortController());

const groupSources = computed(() => chatStore.groupList);
const tradeStatus = computed(() => route.query.trade_status as string);
const token = computed(() => route.query.token as string);
const isLogin = computed(() => authStore.isLogin);
const usingPlugin = computed(() => chatStore.currentPlugin);
const dataSources = computed(() => chatStore.chatList);
const activeGroupId = computed(() => chatStore.active);
const isPdfRoute = computed(() => route.path === '/pdf');
const isTeacherChat = computed(() => route.path === '/teacher/chat');
const activeAppId = computed(() => activeGroupInfo.value?.appId || 0);

// 注意：模型信息相关的计算属性已移动到 useModelInfo composable 中

// 新增的计算属性
const welcomeMode = computed(() => {
  if (isTeacherChat.value) return 'teacher';
  if (route.path.includes('/student')) return 'student';
  if (activeAppId.value > 0) return 'app';
  return 'default';
});

const activeAppInfo = computed<AppInfo | undefined>(() => {
  if (activeAppId.value > 0) {
    return {
      id: activeAppId.value,
      name: activeGroupInfo.value?.title || 'AI应用',
      description: '专业的AI应用服务',
      avatar: ''
    };
  }
  return undefined;
});

const isWelcomeOnlyMode = computed(() => {
  // 在教师聊天页面且没有消息时，只显示欢迎界面，不显示输入框
  return isTeacherChat.value && !chatState.value.messages.length && !activeAppId.value;
});

const showModelSelector = computed(() => {
  // 根据用户角色和页面类型决定是否显示模型选择器
  return !isTeacherChat.value && !activeAppId.value;
});

const allowFileUpload = computed(() => {
  // 根据当前模型配置决定是否允许文件上传
  return chatState.value.modelConfig?.isFileUpload === 1;
});

// 组合函数
const { addGroupChat, updateGroupChat, updateGroupChatSome } = useChat();

// 函数定义

const checkIfBottomVisible = () => {
  const element = scrollRef.value;
  if (!element) return;

  // 检查是否允许滚动
  const canScroll = element.scrollHeight > element.clientHeight;

  if (!canScroll) {
    isAtBottom.value = true; // 如果不能滚动，直接设置为true
    return;
  }

  // 检查是否已经滚动到底部
  const rect = bottomContainer.value.getBoundingClientRect();
  isAtBottom.value = rect.top < window.innerHeight;
};

// 滚动到底部
const handleScrollBtm = () => {
  bottomContainer.value.scrollIntoView({ behavior: 'smooth' });
};

const createNewChatGroup = inject('createNewChatGroup', () =>
  Promise.resolve()
) as () => Promise<void>;

// 会话
const onConversation = async ({
  msg,
  action,
  drawId,
  customId,
  model,
  modelName,
  modelType,
  modelAvatar,
  appId,
  extraParam,
  fileUrl,
  chatId,
}: // isEdit,
Chat.ConversationParams) => {
  if (groupSources.value.length === 0) {
    // await nextTick();
    await createNewChatGroup();
    // await chatStore.queryMyGroup();
  }

  console.log(action);

  if (chatId) {
    await chatStore.deleteChatsAfterId(chatId);
    componentKey.value += 1;
  }
  chatStore.setStreamIn(true);
  const useModelName = modelName || activeModelName.value;
  const useModelType = modelType || activeModelKeyType.value || 1;
  const useModelAvatar = modelAvatar || activeModelAvatar.value;
  const useAppId = appId || activeAppId.value;
  let message = msg || '提问';

  let useModel = model || activeModel.value;
  controller.value = new AbortController();

  if (usingPlugin.value?.deductType && usingPlugin.value?.deductType !== 0) {
    useModel = usingPlugin.value?.parameters;
  }

  /* 增加一条用户记录 */
  addGroupChat({
    text: message,
    model: useModel,
    modelName: modelName,
    modelType: modelType,
    inversion: true,
    fileInfo: fileUrl,
  });

  let options: any = {
    groupId: +activeGroupId.value,
    fileParsing: fileParsing.value,
  };

  /* 虚拟增加一条ai记录 */
  addGroupChat({
    text: '',
    model: useModel,
    modelName: modelName,
    modelType: modelType,
    loading: true,
    inversion: false,
    error: false,
    modelAvatar: useModelAvatar,
    status: 2,
    pluginParam: usingPlugin.value?.parameters,
  });

  await scrollToBottom();
  const timer: any = null;
  let data: any = null;
  chatStore.setStreamIn(true);
  // isStreamIn.value = true;
  useGlobalStore.updateIsChatIn(true);

  const fetchChatAPIOnce = async () => {
    if (
      useModel === 'dall-e-3' ||
      useModel === 'midjourney' ||
      useModel === 'suno-music' ||
      useModel === 'stable-diffusion' ||
      useModel === 'luma-video' ||
      useModel === 'cog-video' ||
      useModel.includes('flux') ||
      useModel === 'ai-ppt'
    ) {
      await handleSingleResponseModel();
    } else {
      await handleStreamResponseModel();
    }
  };

  const handleSingleResponseModel = async () => {
    try {
      const response = await fetchChatAPIProcess({
        prompt: message,
        fileInfo: fileUrl || '',
        model: useModel,
        modelName: useModelName,
        modelAvatar: useModelAvatar,
        modelType: 2,
        action: action,
        drawId: drawId || '',
        customId: customId,
        options,
        usingPluginId: usingPlugin.value?.pluginId || 0,
        extraParam: extraParam || {},
        signal: controller.value.signal,
      });

      processSingleResponse(response);
    } catch (e) {
      // console.log(e);
    }
  };

  const handleStreamResponseModel = async () => {
    let fullText = ''; // 用于累加整个会话的内容
    let lastProcessedIndex = 0;

    try {
      await fetchChatAPIProcess({
        model: useModel,
        modelName: useModelName,
        modelType: useModelType,
        prompt: msg,
        usingPluginId: usingPlugin.value?.pluginId || 0,
        fileInfo: fileUrl,
        appId: useAppId || 0,
        modelAvatar: useModelAvatar,
        options,
        signal: controller.value.signal,
        onDownloadProgress: (progressEvent) => {
          // 兼容不同版本的axios事件结构
          const xhr = progressEvent.event?.target || progressEvent.target;
          const responseText = xhr.responseText;
          const newResponsePart = responseText.substring(lastProcessedIndex);
          lastProcessedIndex = responseText.length; // 更新处理位置

          const responseParts = newResponsePart.trim().split('\n');

          responseParts.forEach((part: string) => {
            try {
              // 尝试将字符串解析为 JSON 对象
              const jsonObj = JSON.parse(part);
              if (jsonObj.userBalance)
                authStore.updateUserBalance(jsonObj.userBalance);
              // 检查 jsonObj 是否具有有效的 text 字段
              if (jsonObj.text) {
                // 累加 text 字段的内容到 fullText
                fullText += jsonObj.text;
              }
            } catch (error) {}
          });
          // 直接使用响应文本更新当前文本
          // 假设responseText是最新获取到的文本块
          // fullText += responseText;

          // 更新UI
          updateGroupChat(dataSources.value.length - 1, {
            chatId: data?.chatId,
            text: fullText, // 使用累加后的全文本更新
            modelType: 1,
            modelName: useModelName,
            error: false,
            loading: true,
            fileInfo: data?.fileInfo,
          });
          // scrollToBottom();
          scrollToBottomIfAtBottom();
        },
      });

      // 流式处理完成后的逻辑
      // processingCompleted();
    } catch (error) {
      handleStreamError(error);
    } finally {
      useGlobalStore.updateIsChatIn(false);
      // typingStatusEnd.value = true;
      await chatStore.queryMyGroup();
      updateGroupChatSome(dataSources.value.length - 1, { loading: false });
    }
  };

  const processSingleResponse = (response: any) => {
    if (!response) return;

    const {
      modelName,
      model,
      drawId,
      customId,
      text,
      status,
      fileInfo,
      userBalance,
      pluginParam,
    } = response;

    const index = dataSources.value.length - 1;
    const data = {
      text,
      model,
      modelType: 2,
      modelName,
      inversion: false,
      drawId,
      customId,
      error: false,
      loading: false,
      fileInfo,
      status,
      pluginParam,
    };

    updateGroupChat(index, data);
    scrollToBottom();

    if (Object.keys(userBalance).length) {
      authStore.updateUserBalance(userBalance);
    }
  };

  const handleStreamError = (error: any) => {
    console.error('fetchChatAPIProcess error:', error);
    useGlobalStore.updateIsChatIn(false);
    clearInterval(timer);
    chatStore.setStreamIn(false);
    // isStreamIn.value = false;

    if (
      error.code === 402 ||
      error?.message.includes('余额不足') ||
      error?.message.includes('体验额度使用完毕')
    ) {
      if (!isLogin.value) {
        authStore.setLoginDialog(true);
      } else {
        useGlobalStore.updateGoodsDialog(true);
      }
    }

    if (error.message.includes('手机号绑定')) {
      if (!isLogin.value) {
        authStore.setLoginDialog(true);
      } else {
        useGlobalStore.updatePhoneDialog(true);
      }
    }

    if (error.message.includes('实名认证')) {
      if (!isLogin.value) {
        authStore.setLoginDialog(true);
      } else {
        useGlobalStore.updateIdentityDialog(true);
      }
    }

    if (error.message.includes('违规')) {
      useGlobalStore.UpdateBadWordsDialog(true);
    }

    if (error?.message.includes('canceled')) {
      updateGroupChatSome(dataSources.value.length - 1, { loading: false });
      scrollToBottomIfAtBottom();
      setTimeout(() => {
        authStore.getUserBalance();
      }, 200);
      return;
    }

    const currentChat = dataSources.value[dataSources.value.length - 1];
    updateGroupChat(dataSources.value.length - 1, {
      chatId: data?.chatId,
      ttsUrl: data?.ttsUrl,
      taskData: data?.taskData,
      videoUrl: data?.videoUrl,
      audioUrl: data?.audioUrl,
      status: data?.status,
      action: data?.action,
      text: currentChat.text === '' ? '' : currentChat.text,
      inversion: false,
      loading: false,
      fileInfo: data?.fileInfo,
      conversationOptions: null,
      pluginParam: data?.pluginParam,
    });
    scrollToBottomIfAtBottom();
  };

  await fetchChatAPIOnce();
  //chatStore.queryMyGroup();
  chatStore.setStreamIn(false);

  // 延迟3秒
  await new Promise((resolve) => setTimeout(resolve, 3000));
  await chatStore.queryActiveChatLogList();
  await new Promise((resolve) => setTimeout(resolve, 200));
  await scrollToBottom();
};

// 停止回复
const pauseRequest = () => {
  controller.value.abort();
  chatStore.setStreamIn(false);
  setTimeout(scrollToBottom, 1000);
};

// 其他登录方式
const otherLoginByToken = async (token: string) => {
  try {
    // 设置用户 token
    authStore.setToken(token);

    // 跳转到 Chat 页面
    await router.replace({ name: 'Chat', query: {} });

    // 显示成功消息
    ms.success('账户登录成功、开始体验吧！');

    // 获取用户信息
    await authStore.getUserInfo();
  } catch (error) {
    // 错误处理
    console.error('登录过程中发生错误:', error);
  }
};

// 支付回调处理
const handleRefresh = async () => {
  // 检查交易状态是否成功
  if (tradeStatus.value.toLowerCase().includes('success')) {
    // 显示成功消息
    ms.success('感谢你的购买、祝您使用愉快~', { duration: 5000 });

    // 获取用户信息
    await authStore.getUserInfo();

    // 跳转到 Chat 页面
    router.replace({ name: 'Chat', query: {} });
  } else {
    // 显示错误消息
    ms.error('您还没有购买成功哦~');
  }
};

const componentKey = ref(0);

// 删除
const handleDelete = async ({ chatId }: Chat.Chat) => {
  dialog.warning({
    title: t('chat.deleteMessage'),
    content: t('chat.deleteMessageConfirm'),
    positiveText: t('common.yes'),
    negativeText: t('common.no'),
    onPositiveClick: async () => {
      await chatStore.deleteChatById(chatId);
      // await chatStore.queryActiveChatLogList();

      componentKey.value += 1;
      // await chatStore.queryActiveChatLogList();
      // await nextTick(async () => {
      //   await chatStore.queryActiveChatLogList();
      // });
      ms.success(t('chat.deleteSuccess'));
    },
  });
};

// // 删除
// const handleDeleteAfterId = async (chatId: number) => {
//   console.log('chatId', chatId);
//   await chatStore.deleteChatsAfterId(chatId);
//   componentKey.value += 1;
// };

// const handleTextSelected = (processedText: string) => {
//   console.log('processedText:', processedText);
//   prompt.value = processedText;
//   nextTick(() => {
//     // 调用autoResize函数，传入textarea元素以重置其高度
//     if (inputRef.value) {
//       autoResize(inputRef.value);
//     }
//   });
//   // autoResize({ target: inputRef.value });

//   // 或者，你也可以直接传入文本框元素
//   // autoResize({ target: $refs.inputRef });
// };

const handleRegenerate = async (index: number, chatId: number) => {
  console.log('index', index);
  if (chatStore.groupList.length === 0 || index === 0) return;
  let message = '';
  /* 如果有index就是重新生成 */
  if (index && typeof index === 'number') {
    const { text, inversion } = dataSources.value[index - 1];
    if (text) {
      message = text;
    }
    if (!inversion) {
      return;
    }
  }
  onConversation({ msg: message, chatId: chatId - 1 });
  scrollToBottom();
};

// 新的事件处理方法

const handleFeatureClick = (feature: any) => {
  console.log('Feature clicked:', feature);
  // 可以根据功能类型执行相应操作
};

const handleSendMessage = async (data: { text: string; model?: ModelInfo; files?: File[] }) => {
  try {
    await sendMessage(data.text, {
      model: data.model?.id,
      fileUrl: data.files?.[0] ? 'uploaded-file-url' : undefined
    });
  } catch (error) {
    console.error('发送消息失败:', error);
  }
};

const handleClearInput = () => {
  updateInput('');
};

const handleModelChange = (model: ModelInfo) => {
  console.log('模型已切换:', model);
  ms.success(`已切换到 ${model.name}`);
};

const handleFileUpload = (files: File[]) => {
  uploadFiles(files);
};

const handleAppSelect = (app: AppInfo) => {
  console.log('应用已选择:', app);
  // 可以切换到对应的应用模式
};

watch(
  dataSources,
  (val) => {
    if (val.length === 0) return;
    if (firstScroll.value) {
      firstScroll.value = false;
      scrollToBottom();
    }
  },
  { immediate: true }
);

watch(
  scrollRef,
  () => {
    checkIfBottomVisible();
  },
  { flush: 'post' }
);

onMounted(async () => {
  if (token.value) otherLoginByToken(token.value);
  if (tradeStatus.value) handleRefresh();
  // if (!isLogin.value) authStore.setLoginDialog(true);
  await nextTick(async () => {
    await chatStore.queryActiveChatLogList();
  });

  // 添加滚动事件监听器
});

onMounted(() => {
  const scrollElement = scrollRef.value;
  if (scrollElement) {
    scrollElement.addEventListener('scroll', checkIfBottomVisible);
  }

  // 触发侧边栏高度更新，确保与输入框对齐
  setTimeout(() => {
    window.dispatchEvent(new CustomEvent('update-sidebar-height'));
  }, 200);

  // 监听来自快速开始侧边栏的提示事件
  window.addEventListener('use-prompt', handlePromptEvent);
});

// 添加清理事件监听
onUnmounted(() => {
  window.removeEventListener('use-prompt', handlePromptEvent);
});

// 处理来自快速开始侧边栏的提示事件
const handlePromptEvent = (event: any) => {
  const { prompt } = event.detail;
  if (prompt) {
    handleUsePrompt(prompt);
  }
};

// 清空当前对话内容
const clearConversation = () => {
  chatStore.clearChatList();
  componentKey.value += 1;
};

// 设置当前活动的应用ID
const setActiveAppId = (appId: number) => {
  chatStore.setActiveAppId(appId);
};

// 处理快速提示
const handleUsePrompt = (promptText: string) => {
  onConversation({
    msg: promptText,
    action: '',
    drawId: '',
    customId: '',
    model: '',
    modelName: '',
    modelType: 0,
    modelAvatar: '',
    appId: 0,
    extraParam: {},
    fileUrl: '',
    chatId: 0
  });
};

// 提供onConversation给后代组件
provide('onConversation', onConversation);
provide('handleRegenerate', handleRegenerate);
provide('clearConversation', clearConversation);
provide('setActiveAppId', setActiveAppId);
provide('activeAppId', activeAppId);
</script>

<template>
  <div class="h-full flex flex-col bg-transparent relative overflow-hidden">
    <!-- 美化的背景装饰层 -->
    <div class="absolute inset-0 pointer-events-none overflow-hidden">
      <!-- 顶部装饰渐变 -->
      <div class="absolute top-0 left-0 right-0 h-24 bg-gradient-to-b from-blue-50/20 via-indigo-50/10 to-transparent dark:from-blue-900/15 dark:via-indigo-900/8 dark:to-transparent"></div>

      <!-- 底部装饰渐变 -->
      <div class="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-purple-50/15 via-transparent to-transparent dark:from-purple-900/10 dark:via-transparent dark:to-transparent"></div>

      <!-- 微妙的装饰点 - 增强版 -->
      <div class="absolute top-6 right-12 w-2 h-2 rounded-full bg-blue-400/25 dark:bg-blue-400/35 animate-pulse"></div>
      <div class="absolute top-16 right-20 w-1 h-1 rounded-full bg-purple-400/30 dark:bg-purple-400/40"></div>
      <div class="absolute bottom-20 left-12 w-3 h-3 rounded-full bg-green-400/20 dark:bg-green-400/30"></div>
      <div class="absolute top-1/3 left-6 w-1.5 h-1.5 rounded-full bg-orange-400/25 dark:bg-orange-400/35"></div>
      <div class="absolute bottom-1/3 right-8 w-2.5 h-2.5 rounded-full bg-teal-400/20 dark:bg-teal-400/30"></div>
    </div>

    <!-- 聊天工具栏 -->
    <ChatToolbar v-if="!isPdfRoute" class="relative z-10" />

    <main class="flex-1 overflow-hidden relative z-10">
      <div
        id="scrollRef"
        ref="scrollRef"
        class="relative h-full overflow-hidden scroll-smooth transition-all duration-300 bg-gradient-to-b from-transparent via-blue-50/3 to-purple-50/3 dark:via-blue-900/5 dark:to-purple-900/5"
      >
        <div
          id="image-wrapper"
          class="w-full m-auto h-full mx-auto animate__animated animate__fadeIn relative"
          :class="[isMobile ? 'px-4 py-3' : 'px-6 py-4 w-full']"
          :style="{ maxWidth: '64rem' }"
        >
          <!-- 内容区域装饰背景 -->
          <div class="absolute inset-0 pointer-events-none">
            <!-- 左侧装饰线 -->
            <div class="absolute left-0 top-8 bottom-8 w-px bg-gradient-to-b from-transparent via-blue-200/30 to-transparent dark:via-blue-600/20"></div>
            <!-- 右侧装饰线 -->
            <div class="absolute right-0 top-8 bottom-8 w-px bg-gradient-to-b from-transparent via-purple-200/30 to-transparent dark:via-purple-600/20"></div>
          </div>
          <!-- 欢迎界面 - 使用统一的WelcomeScreen组件 -->
          <template v-if="!chatState.messages.length">
            <div
              class="flex justify-center items-center text-center animate__animated animate__fadeIn"
              :class="[isMobile ? 'h-full' : 'h-4/5 ']"
            >
              <WelcomeScreen
                :mode="welcomeMode"
                :app-info="activeAppInfo"
                @use-prompt="handleUsePrompt"
                @feature-click="handleFeatureClick"
              />
            </div>
          </template>
          <template v-if="dataSources.length">
            <div :key="componentKey" class="animate__animated animate__fadeIn">
              <Message
                v-for="(item, index) of dataSources"
                :index="index"
                :chatId="item.chatId"
                :text="item.text"
                :model="item.model"
                :modelType="item.modelType"
                :modelName="item.modelName"
                :modelAvatar="item.modelAvatar"
                :status="item.status"
                :fileInfo="item.fileInfo"
                :ttsUrl="item.ttsUrl"
                :taskData="item.taskData"
                :videoUrl="item.videoUrl"
                :audioUrl="item.audioUrl"
                :action="item.action"
                :inversion="item.inversion"
                :loading="item.loading"
                :drawId="item.drawId"
                :customId="item.customId"
                :pluginParam="item.pluginParam"
                :promptReference="item.promptReference"
                :isLast="index === dataSources.length - 1"
                @delete="handleDelete(item)"
              />
              <!-- 美化的滚动到底部按钮 -->
              <div class="sticky bottom-0 left-0 flex justify-center mb-3 p-1">
                <button
                  v-if="!isAtBottom"
                  @click="handleScrollBtm"
                  class="bg-white/95 dark:bg-gray-800/95 p-3.5 ds-shadow-xl rounded-full border border-white/50 dark:border-gray-600/60 text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 hover-float transform hover:scale-110 active:scale-95 flex items-center justify-center backdrop-blur-md group relative overflow-hidden"
                >
                  <!-- 按钮背景光晕 -->
                  <div class="absolute inset-0 rounded-full bg-gradient-to-br from-blue-500/10 via-transparent to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  <!-- 增强的向下箭头图标 -->
                  <div class="relative z-10">
                    <DownSmall size="22" theme="outline" :strokeWidth="2.5" class="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-300 group-hover:drop-shadow-sm" />
                    <!-- 装饰光点 -->
                    <div class="absolute -top-1 -right-1 w-2 h-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 scale-0 group-hover:scale-100 animate-pulse"></div>
                  </div>

                  <!-- 底部装饰线 -->
                  <div class="absolute bottom-0 left-1/4 right-1/4 h-px bg-gradient-to-r from-transparent via-blue-400/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
              </div>
            </div>
          </template>
          <div ref="bottomContainer" class="bottom" />
        </div>
      </div>
    </main>
    <!-- 错误处理组件 -->
    <ErrorHandler
      :visible="chatState.error.hasError"
      :error-type="chatState.error.errorType"
      :error-message="chatState.error.errorMessage"
      :can-retry="chatState.error.canRetry"
      :retry-count="chatState.error.retryCount"
      :auto-retry="true"
      :max-auto-retries="3"
      @retry="retryLastMessage"
      @dismiss="clearError"
      class="relative z-10 mb-4"
    />

    <!-- 聊天输入组件：使用新的统一输入组件 -->
    <ChatInput
      v-if="!isWelcomeOnlyMode"
      v-model="chatState.input.text"
      :disabled="chatState.ui.isStreamIn"
      :is-loading="chatState.ui.isStreamIn"
      :has-error="chatState.error.hasError"
      :can-retry="chatState.error.canRetry"
      :can-stop="chatState.ui.isStreamIn"
      :selected-model="chatState.modelConfig"
      :available-models="availableModels"
      :show-model-selector="showModelSelector"
      :allow-file-upload="allowFileUpload"
      :allow-voice-input="false"
      :quick-prompts="quickPrompts"
      :app-suggestions="appSuggestions"
      :show-app-suggestions="showAppSuggestions"
      @send="handleSendMessage"
      @stop="stopCurrentRequest"
      @retry="retryLastMessage"
      @clear="handleClearInput"
      @model-change="handleModelChange"
      @file-upload="handleFileUpload"
      @app-select="handleAppSelect"
      class="relative z-10"
    />
  </div>
</template>

<style lang="less">
@keyframes rotateAnimation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

::v-deep .rotate-icon {
  animation: rotateAnimation 1.5s ease-in-out infinite;
}

/* 滚动到底部按钮动画 */
.hover-float {
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
}
</style>
