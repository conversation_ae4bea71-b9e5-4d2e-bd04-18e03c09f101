<!--
  输入区域组件
  负责文本输入、自动调整高度、快捷键处理等核心输入功能
-->
<template>
  <div class="input-area-container">
    <!-- 输入框容器 -->
    <div
      class="input-wrapper"
      :class="[
        'relative flex items-end bg-white dark:bg-gray-800 rounded-xl border transition-all duration-200',
        {
          'border-blue-300 shadow-lg': focused,
          'border-gray-200 dark:border-gray-600': !focused,
          'border-red-300': hasError,
          'opacity-50 cursor-not-allowed': disabled
        }
      ]"
    >
      <!-- 左侧操作按钮 -->
      <div class="flex items-center pl-3 space-x-2">
        <slot name="left-actions" />
      </div>

      <!-- 主输入区域 -->
      <div class="flex-1 relative">
        <!-- 文本输入框 -->
        <textarea
          ref="textareaRef"
          v-model="inputText"
          :placeholder="placeholder"
          :disabled="disabled"
          :maxlength="maxLength"
          class="input-textarea"
          :class="[
            'w-full border-0 bg-transparent resize-none outline-none',
            'text-gray-800 dark:text-gray-200',
            'placeholder:text-gray-400 dark:placeholder:text-gray-500',
            'px-3 py-3 text-base leading-6',
            'transition-all duration-200',
            isMobile ? 'text-base' : 'text-sm'
          ]"
          :style="textareaStyle"
          @input="handleInput"
          @keydown="handleKeydown"
          @keyup="handleKeyup"
          @focus="handleFocus"
          @blur="handleBlur"
          @paste="handlePaste"
          @compositionstart="handleCompositionStart"
          @compositionend="handleCompositionEnd"
        />

        <!-- 字符计数 -->
        <div
          v-if="showCharCount && maxLength"
          class="absolute bottom-1 right-3 text-xs text-gray-400 pointer-events-none"
        >
          {{ inputText.length }}/{{ maxLength }}
        </div>

        <!-- 输入提示 -->
        <div
          v-if="showInputHint && !inputText && !focused"
          class="absolute inset-0 flex items-center justify-center pointer-events-none"
        >
          <div class="text-sm text-gray-400 dark:text-gray-500 text-center px-4">
            <div class="mb-1">💡 小贴士</div>
            <div class="text-xs">
              {{ isMobile ? '点击输入您的问题' : '输入问题，按 Enter 发送，Shift+Enter 换行' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧操作按钮 -->
      <div class="flex items-center pr-3 space-x-2">
        <slot name="right-actions" />

        <!-- 发送按钮 -->
        <button
          :disabled="!canSend"
          class="send-button"
          :class="[
            'flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200',
            {
              'bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg transform hover:scale-105': canSend,
              'bg-gray-200 dark:bg-gray-600 text-gray-400 cursor-not-allowed': !canSend
            }
          ]"
          @click="handleSend"
        >
          <svg v-if="!isLoading" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.429a1 1 0 001.17-1.409l-7-14z"/>
          </svg>
          <div v-else class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </button>
      </div>
    </div>

    <!-- 输入状态提示 -->
    <div v-if="statusMessage" class="status-message mt-2 px-3">
      <div
        class="text-sm flex items-center space-x-2"
        :class="{
          'text-blue-600 dark:text-blue-400': statusType === 'info',
          'text-red-600 dark:text-red-400': statusType === 'error',
          'text-green-600 dark:text-green-400': statusType === 'success',
          'text-yellow-600 dark:text-yellow-400': statusType === 'warning'
        }"
      >
        <div class="flex-shrink-0">
          <svg v-if="statusType === 'error'" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
          <svg v-else-if="statusType === 'success'" class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
          <svg v-else class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
          </svg>
        </div>
        <span>{{ statusMessage }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'

interface Props {
  modelValue: string
  placeholder?: string
  disabled?: boolean
  maxLength?: number
  minRows?: number
  maxRows?: number
  showCharCount?: boolean
  showInputHint?: boolean
  isLoading?: boolean
  hasError?: boolean
  statusMessage?: string
  statusType?: 'info' | 'error' | 'success' | 'warning'
  autoFocus?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'send', value: string): void
  (e: 'paste', event: ClipboardEvent): void
  (e: 'focus'): void
  (e: 'blur'): void
  (e: 'input', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入您的问题...',
  disabled: false,
  maxLength: 4000,
  minRows: 1,
  maxRows: 8,
  showCharCount: true,
  showInputHint: true,
  isLoading: false,
  hasError: false,
  statusType: 'info',
  autoFocus: false
})

const emit = defineEmits<Emits>()

const { isMobile } = useBasicLayout()

// 响应式数据
const textareaRef = ref<HTMLTextAreaElement>()
const focused = ref(false)
const isComposing = ref(false)

// 双向绑定
const inputText = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 计算属性
const canSend = computed(() => {
  return !props.disabled &&
         !props.isLoading &&
         inputText.value.trim().length > 0 &&
         !isComposing.value
})

const textareaStyle = computed(() => {
  const lineHeight = 24 // 1.5rem
  const minHeight = lineHeight * props.minRows + 24 // padding
  const maxHeight = lineHeight * props.maxRows + 24

  return {
    minHeight: `${minHeight}px`,
    maxHeight: `${maxHeight}px`,
    overflowY: 'auto'
  }
})

// 事件处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  emit('input', target.value)
  autoResize()
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (event.shiftKey) {
      // Shift+Enter 换行
      return
    } else {
      // Enter 发送
      event.preventDefault()
      if (canSend.value) {
        handleSend()
      }
    }
  }
}

const handleKeyup = (event: KeyboardEvent) => {
  // 处理其他按键事件
}

const handleFocus = () => {
  focused.value = true
  emit('focus')
}

const handleBlur = () => {
  focused.value = false
  emit('blur')
}

const handlePaste = (event: ClipboardEvent) => {
  emit('paste', event)
}

const handleCompositionStart = () => {
  isComposing.value = true
}

const handleCompositionEnd = () => {
  isComposing.value = false
}

const handleSend = () => {
  if (canSend.value) {
    emit('send', inputText.value.trim())
  }
}

// 自动调整高度
const autoResize = async () => {
  await nextTick()
  if (!textareaRef.value) return

  const textarea = textareaRef.value
  const lineHeight = 24
  const minHeight = lineHeight * props.minRows + 24
  const maxHeight = lineHeight * props.maxRows + 24

  // 重置高度以获取正确的scrollHeight
  textarea.style.height = `${minHeight}px`

  // 计算内容高度
  const scrollHeight = textarea.scrollHeight
  const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight)

  textarea.style.height = `${newHeight}px`

  // 如果内容超过最大高度，显示滚动条
  if (scrollHeight > maxHeight) {
    textarea.style.overflowY = 'auto'
  } else {
    textarea.style.overflowY = 'hidden'
  }
}

// 聚焦到输入框
const focus = () => {
  textareaRef.value?.focus()
}

// 清空输入
const clear = () => {
  emit('update:modelValue', '')
  nextTick(() => {
    autoResize()
  })
}

// 监听内容变化自动调整高度
watch(() => props.modelValue, () => {
  nextTick(() => {
    autoResize()
  })
})

// 组件挂载后处理
onMounted(() => {
  if (props.autoFocus) {
    nextTick(() => {
      focus()
    })
  }
  autoResize()
})

// 暴露方法给父组件
defineExpose({
  focus,
  clear,
  textareaRef
})
</script>

<style scoped>
.input-area-container {
  @apply w-full;
}

.input-wrapper {
  @apply relative;
  min-height: 48px;
}

.input-textarea {
  @apply resize-none;
  field-sizing: content;
}

.input-textarea::-webkit-scrollbar {
  @apply w-1;
}

.input-textarea::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.input-textarea::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.input-textarea::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

.send-button {
  @apply transition-all duration-200 ease-in-out;
}

.send-button:hover:not(:disabled) {
  @apply transform scale-105;
}

.status-message {
  animation: slideInFromTop 0.2s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .input-wrapper {
    @apply rounded-lg;
  }

  .input-textarea {
    @apply text-base;
    /* 防止iOS缩放 */
    font-size: 16px;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .input-wrapper {
    @apply bg-gray-800 border-gray-600;
  }

  .input-textarea {
    @apply text-gray-200 placeholder:text-gray-400;
  }
}
</style>
