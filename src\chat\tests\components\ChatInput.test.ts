/**
 * ChatInput组件测试
 * 验证新的聊天输入组件功能
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import ChatInput from '@/components/ChatInput/index.vue'
import InputArea from '@/components/ChatInput/InputArea.vue'
import FileUpload from '@/components/ChatInput/FileUpload.vue'
import ModelSelector from '@/components/ChatInput/ModelSelector.vue'

// Mock naive-ui
vi.mock('naive-ui', () => ({
  useMessage: () => ({
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  })
}))

// Mock hooks
vi.mock('@/hooks/useBasicLayout', () => ({
  useBasicLayout: () => ({
    isMobile: false
  })
}))

describe('ChatInput组件', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(ChatInput, {
      props: {
        modelValue: '',
        disabled: false,
        isLoading: false,
        hasError: false,
        canRetry: false,
        canStop: false
      }
    })
  })

  it('应该正确渲染', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.chat-input-container').exists()).toBe(true)
  })

  it('应该支持双向绑定', async () => {
    const input = wrapper.findComponent(InputArea)
    
    // 模拟输入
    await input.vm.$emit('update:modelValue', '测试消息')
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')[0]).toEqual(['测试消息'])
  })

  it('应该在禁用状态下阻止操作', async () => {
    await wrapper.setProps({ disabled: true })
    
    const input = wrapper.findComponent(InputArea)
    expect(input.props('disabled')).toBe(true)
  })

  it('应该正确处理发送事件', async () => {
    const input = wrapper.findComponent(InputArea)
    
    await input.vm.$emit('send', '测试消息')
    
    expect(wrapper.emitted('send')).toBeTruthy()
    expect(wrapper.emitted('send')[0][0]).toEqual({
      text: '测试消息',
      model: undefined,
      files: undefined
    })
  })

  it('应该显示模型选择器（当启用时）', async () => {
    await wrapper.setProps({ 
      showModelSelector: true,
      availableModels: [
        { id: '1', name: 'GPT-4', description: '最新模型', category: 'OpenAI' }
      ]
    })
    
    expect(wrapper.findComponent(ModelSelector).exists()).toBe(true)
  })

  it('应该处理文件上传', async () => {
    await wrapper.setProps({ allowFileUpload: true })
    
    const fileUpload = wrapper.findComponent(FileUpload)
    expect(fileUpload.exists()).toBe(true)
    
    const mockFiles = [new File(['test'], 'test.txt', { type: 'text/plain' })]
    await fileUpload.vm.$emit('upload', mockFiles)
    
    expect(wrapper.emitted('file-upload')).toBeTruthy()
    expect(wrapper.emitted('file-upload')[0]).toEqual([mockFiles])
  })
})

describe('InputArea组件', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(InputArea, {
      props: {
        modelValue: '',
        placeholder: '输入消息...',
        disabled: false,
        maxLength: 1000
      }
    })
  })

  it('应该正确渲染输入框', () => {
    expect(wrapper.find('textarea').exists()).toBe(true)
    expect(wrapper.find('.send-button').exists()).toBe(true)
  })

  it('应该自动调整高度', async () => {
    const textarea = wrapper.find('textarea')
    
    // 模拟长文本输入
    await wrapper.setProps({ modelValue: '这是一个很长的消息\n'.repeat(10) })
    await nextTick()
    
    // 验证高度调整逻辑被调用
    expect(textarea.element.style.height).toBeTruthy()
  })

  it('应该处理快捷键', async () => {
    const textarea = wrapper.find('textarea')
    
    // 模拟Enter键发送
    await textarea.trigger('keydown', { key: 'Enter' })
    
    // 由于没有内容，不应该触发发送
    expect(wrapper.emitted('send')).toBeFalsy()
    
    // 添加内容后再试
    await wrapper.setProps({ modelValue: '测试消息' })
    await textarea.trigger('keydown', { key: 'Enter' })
    
    expect(wrapper.emitted('send')).toBeTruthy()
  })

  it('应该显示字符计数', async () => {
    await wrapper.setProps({ 
      modelValue: '测试',
      showCharCount: true,
      maxLength: 100
    })
    
    expect(wrapper.text()).toContain('2/100')
  })
})

describe('FileUpload组件', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(FileUpload, {
      props: {
        disabled: false,
        allowMultiple: true,
        maxSize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5
      }
    })
  })

  it('应该正确渲染上传按钮', () => {
    expect(wrapper.find('.upload-button').exists()).toBe(true)
    expect(wrapper.find('input[type="file"]').exists()).toBe(true)
  })

  it('应该处理文件选择', async () => {
    const fileInput = wrapper.find('input[type="file"]')
    const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' })
    
    Object.defineProperty(fileInput.element, 'files', {
      value: [mockFile],
      writable: false
    })
    
    await fileInput.trigger('change')
    
    expect(wrapper.emitted('upload')).toBeTruthy()
  })

  it('应该验证文件大小', async () => {
    const wrapper = mount(FileUpload, {
      props: {
        maxSize: 1024 // 1KB限制
      }
    })
    
    const largeFile = new File(['x'.repeat(2048)], 'large.txt', { type: 'text/plain' })
    
    // 模拟文件处理
    await wrapper.vm.processFiles([largeFile])
    
    expect(wrapper.emitted('error')).toBeTruthy()
  })

  it('应该支持拖拽上传', async () => {
    const dropZone = wrapper.find('.upload-button')
    
    await dropZone.trigger('dragover')
    expect(wrapper.vm.isDragOver).toBe(true)
    
    await dropZone.trigger('dragleave')
    expect(wrapper.vm.isDragOver).toBe(false)
  })
})

describe('ModelSelector组件', () => {
  const mockModels = [
    { id: '1', name: 'GPT-4', description: 'OpenAI最新模型', category: 'OpenAI' },
    { id: '2', name: 'Claude', description: 'Anthropic模型', category: 'Anthropic' }
  ]

  let wrapper: any

  beforeEach(() => {
    wrapper = mount(ModelSelector, {
      props: {
        models: mockModels,
        disabled: false
      }
    })
  })

  it('应该正确渲染模型按钮', () => {
    expect(wrapper.find('.model-button').exists()).toBe(true)
  })

  it('应该显示下拉菜单', async () => {
    await wrapper.find('.model-button').trigger('click')
    
    expect(wrapper.find('.dropdown-menu').exists()).toBe(true)
    expect(wrapper.findAll('.model-option')).toHaveLength(mockModels.length)
  })

  it('应该支持模型搜索', async () => {
    await wrapper.find('.model-button').trigger('click')
    
    const searchInput = wrapper.find('input[placeholder="搜索模型..."]')
    await searchInput.setValue('GPT')
    
    // 验证过滤结果
    const visibleOptions = wrapper.findAll('.model-option:not([style*="display: none"])')
    expect(visibleOptions.length).toBeLessThanOrEqual(mockModels.length)
  })

  it('应该处理模型选择', async () => {
    await wrapper.find('.model-button').trigger('click')
    
    const firstOption = wrapper.find('.model-option')
    await firstOption.trigger('click')
    
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('change')).toBeTruthy()
  })
})

describe('集成测试', () => {
  it('应该正确处理完整的发送流程', async () => {
    const wrapper = mount(ChatInput, {
      props: {
        modelValue: '',
        showModelSelector: true,
        allowFileUpload: true,
        availableModels: [
          { id: '1', name: 'GPT-4', description: '测试模型', category: 'Test' }
        ]
      }
    })

    // 1. 输入文本
    const inputArea = wrapper.findComponent(InputArea)
    await inputArea.vm.$emit('update:modelValue', '测试消息')

    // 2. 选择模型
    const modelSelector = wrapper.findComponent(ModelSelector)
    await modelSelector.vm.$emit('change', { id: '1', name: 'GPT-4' })

    // 3. 发送消息
    await inputArea.vm.$emit('send', '测试消息')

    // 验证事件
    expect(wrapper.emitted('send')).toBeTruthy()
    expect(wrapper.emitted('model-change')).toBeTruthy()
    
    const sendEvent = wrapper.emitted('send')[0][0]
    expect(sendEvent.text).toBe('测试消息')
    expect(sendEvent.model).toEqual({ id: '1', name: 'GPT-4' })
  })
})
