<!--
  统一的聊天输入组件
  整合输入区域、文件上传、模型选择等功能，提供一致的用户体验
-->
<template>
  <div class="chat-input-container">
    <!-- 模型选择器 (可选) -->
    <div v-if="showModelSelector" class="model-selector-wrapper mb-3">
      <ModelSelector
        v-model="selectedModel"
        :disabled="disabled || isLoading"
        :models="availableModels"
        @change="handleModelChange"
      />
    </div>

    <!-- 主输入区域 -->
    <div class="input-section">
      <InputArea
        v-model="inputText"
        :placeholder="computedPlaceholder"
        :disabled="disabled || isLoading"
        :max-length="maxLength"
        :is-loading="isLoading"
        :has-error="hasError"
        :status-message="statusMessage"
        :status-type="statusType"
        :auto-focus="autoFocus"
        @send="handleSend"
        @paste="handlePaste"
        @focus="handleFocus"
        @blur="handleBlur"
        @input="handleInput"
      >
        <!-- 左侧操作按钮 -->
        <template #left-actions>
          <!-- 文件上传 -->
          <FileUpload
            v-if="allowFileUpload"
            :disabled="disabled || isLoading"
            :max-size="maxFileSize"
            :max-files="maxFiles"
            :allowed-extensions="allowedFileTypes"
            @upload="handleFileUpload"
            @error="handleFileError"
          />

          <!-- 语音输入按钮 (未来功能) -->
          <button
            v-if="allowVoiceInput"
            :disabled="disabled || isLoading"
            class="voice-button flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300"
            @click="handleVoiceInput"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"/>
            </svg>
          </button>
        </template>

        <!-- 右侧操作按钮 -->
        <template #right-actions>
          <!-- 停止生成按钮 -->
          <button
            v-if="isLoading && canStop"
            class="stop-button flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-600 dark:text-red-400"
            @click="handleStop"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd"/>
            </svg>
          </button>

          <!-- 清空输入按钮 -->
          <button
            v-if="inputText && !isLoading"
            class="clear-button flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300"
            @click="handleClear"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </template>
      </InputArea>
    </div>

    <!-- 快捷操作栏 -->
    <div v-if="showQuickActions" class="quick-actions mt-3 flex items-center justify-between">
      <!-- 左侧：快捷提示 -->
      <div class="quick-prompts flex items-center space-x-2">
        <button
          v-for="prompt in quickPrompts"
          :key="prompt.id"
          class="quick-prompt-button px-3 py-1.5 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-all duration-200"
          @click="useQuickPrompt(prompt.text)"
        >
          {{ prompt.label }}
        </button>
      </div>

      <!-- 右侧：状态信息 -->
      <div class="status-info flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400">
        <!-- 字符计数 -->
        <span v-if="inputText">{{ inputText.length }}/{{ maxLength }}</span>

        <!-- 当前模型 -->
        <span v-if="selectedModel" class="flex items-center space-x-1">
          <span>模型:</span>
          <span class="font-medium">{{ selectedModel.name }}</span>
        </span>
      </div>
    </div>

    <!-- 应用搜索建议 -->
    <div
      v-if="showAppSuggestions && appSuggestions.length > 0"
      class="app-suggestions mt-2 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 shadow-lg"
    >
      <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        应用建议
      </div>
      <div class="space-y-2">
        <button
          v-for="app in appSuggestions"
          :key="app.id"
          class="app-suggestion w-full flex items-center space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
          @click="selectApp(app)"
        >
          <img
            v-if="app.avatar"
            :src="app.avatar"
            :alt="app.name"
            class="w-8 h-8 rounded-full object-cover"
          />
          <div class="flex-1 text-left">
            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
              {{ app.name }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              {{ app.description }}
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- 错误重试 -->
    <div v-if="hasError && canRetry" class="error-retry mt-3">
      <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
        <div class="flex items-center space-x-2 text-red-700 dark:text-red-400">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
          <span class="text-sm">{{ errorMessage || '发送失败' }}</span>
        </div>
        <button
          class="retry-button px-3 py-1 text-sm bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200"
          @click="handleRetry"
        >
          重试
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import InputArea from './InputArea.vue'
import FileUpload from './FileUpload.vue'
import ModelSelector from './ModelSelector.vue'
import type { ModelInfo, AppInfo } from '@/types/chat'

interface QuickPrompt {
  id: string
  label: string
  text: string
}

interface Props {
  modelValue?: string
  disabled?: boolean
  placeholder?: string
  maxLength?: number
  showModelSelector?: boolean
  allowFileUpload?: boolean
  allowVoiceInput?: boolean
  showQuickActions?: boolean
  autoFocus?: boolean
  isLoading?: boolean
  hasError?: boolean
  canRetry?: boolean
  canStop?: boolean
  errorMessage?: string
  statusMessage?: string
  statusType?: 'info' | 'error' | 'success' | 'warning'
  selectedModel?: ModelInfo
  availableModels?: ModelInfo[]
  quickPrompts?: QuickPrompt[]
  maxFileSize?: number
  maxFiles?: number
  allowedFileTypes?: string[]
  appSuggestions?: AppInfo[]
  showAppSuggestions?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'send', data: { text: string; model?: ModelInfo; files?: File[] }): void
  (e: 'stop'): void
  (e: 'retry'): void
  (e: 'clear'): void
  (e: 'model-change', model: ModelInfo): void
  (e: 'file-upload', files: File[]): void
  (e: 'app-select', app: AppInfo): void
  (e: 'voice-input'): void
  (e: 'focus'): void
  (e: 'blur'): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  placeholder: '',
  maxLength: 4000,
  showModelSelector: false,
  allowFileUpload: true,
  allowVoiceInput: false,
  showQuickActions: true,
  autoFocus: false,
  isLoading: false,
  hasError: false,
  canRetry: false,
  canStop: true,
  statusType: 'info',
  availableModels: () => [],
  quickPrompts: () => [],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5,
  allowedFileTypes: () => [],
  appSuggestions: () => [],
  showAppSuggestions: false
})

const emit = defineEmits<Emits>()
const message = useMessage()
const { isMobile } = useBasicLayout()

// 响应式数据
const inputText = computed({
  get: () => props.modelValue || '',
  set: (value) => emit('update:modelValue', value)
})

const selectedModel = ref<ModelInfo | undefined>(props.selectedModel)
const uploadedFiles = ref<File[]>([])

// 计算属性
const computedPlaceholder = computed(() => {
  if (props.placeholder) return props.placeholder
  if (isMobile.value) return '输入您的问题...'
  return '输入问题，按 Enter 发送，Shift+Enter 换行'
})

// 事件处理
const handleSend = () => {
  if (!inputText.value.trim() && uploadedFiles.value.length === 0) {
    return
  }

  emit('send', {
    text: inputText.value.trim(),
    model: selectedModel.value,
    files: uploadedFiles.value.length > 0 ? uploadedFiles.value : undefined
  })

  // 清空输入和文件
  inputText.value = ''
  uploadedFiles.value = []
}

const handleStop = () => {
  emit('stop')
}

const handleRetry = () => {
  emit('retry')
}

const handleClear = () => {
  inputText.value = ''
  uploadedFiles.value = []
  emit('clear')
}

const handleModelChange = (model: ModelInfo) => {
  selectedModel.value = model
  emit('model-change', model)
}

const handleFileUpload = (files: File[]) => {
  uploadedFiles.value = files
  emit('file-upload', files)
}

const handleFileError = (error: string) => {
  message.error(error)
}

const handleVoiceInput = () => {
  emit('voice-input')
  message.info('语音输入功能即将推出')
}

const handlePaste = (event: ClipboardEvent) => {
  // 处理粘贴事件，可以检测图片等
}

const handleFocus = () => {
  emit('focus')
}

const handleBlur = () => {
  emit('blur')
}

const handleInput = (value: string) => {
  // 处理输入变化，可以触发应用搜索等
}

const useQuickPrompt = (text: string) => {
  inputText.value = text
  message.success('已应用快捷提示')
}

const selectApp = (app: AppInfo) => {
  emit('app-select', app)
  // 可以自动填充相关提示词
  if (app.name) {
    inputText.value = `@${app.name} `
  }
}

// 监听选中模型变化
watch(() => props.selectedModel, (newModel) => {
  selectedModel.value = newModel
})

// 暴露方法给父组件
defineExpose({
  focus: () => {
    // 聚焦到输入框
  },
  clear: handleClear,
  insertText: (text: string) => {
    inputText.value += text
  }
})
</script>

<style scoped>
.chat-input-container {
  @apply w-full space-y-3;
}

.quick-prompt-button {
  @apply transition-all duration-200 ease-in-out;
}

.quick-prompt-button:hover {
  @apply transform scale-105;
}

.app-suggestion {
  @apply transition-all duration-200 ease-in-out;
}

.app-suggestion:hover {
  @apply transform scale-[1.02];
}

.error-retry {
  animation: slideInFromTop 0.2s ease-out;
}

.app-suggestions {
  animation: slideInFromTop 0.2s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .quick-actions {
    @apply flex-col space-y-2;
  }

  .quick-prompts {
    @apply w-full justify-start;
  }

  .status-info {
    @apply w-full justify-end;
  }
}
</style>
