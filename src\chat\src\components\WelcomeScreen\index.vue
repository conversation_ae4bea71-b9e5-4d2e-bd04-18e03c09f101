<!--
  统一的欢迎界面组件
  根据不同模式显示相应的欢迎内容和引导
-->
<template>
  <div class="welcome-screen">
    <!-- 主要内容区域 -->
    <div class="welcome-content">
      <!-- 头部图标和标题 -->
      <div class="welcome-header">
        <div class="welcome-icon">
          <div v-if="mode === 'teacher'" class="icon-container teacher-icon">
            <svg class="w-16 h-16 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
            </svg>
          </div>
          <div v-else-if="mode === 'student'" class="icon-container student-icon">
            <svg class="w-16 h-16 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div v-else-if="appInfo" class="icon-container app-icon">
            <img
              v-if="appInfo.avatar"
              :src="appInfo.avatar"
              :alt="appInfo.name"
              class="w-16 h-16 rounded-full object-cover"
            />
            <div v-else class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
              <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
              </svg>
            </div>
          </div>
          <div v-else class="icon-container default-icon">
            <svg class="w-16 h-16 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </div>

        <div class="welcome-title">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            {{ welcomeTitle }}
          </h1>
          <p class="text-lg text-gray-600 dark:text-gray-400">
            {{ welcomeSubtitle }}
          </p>
        </div>
      </div>

      <!-- 功能介绍卡片 -->
      <div class="feature-cards">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="feature in features"
            :key="feature.id"
            class="feature-card group cursor-pointer"
            @click="handleFeatureClick(feature)"
          >
            <div class="feature-icon">
              <component :is="feature.icon" class="w-8 h-8" />
            </div>
            <div class="feature-content">
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
            </div>
            <div class="feature-arrow">
              <svg class="w-5 h-5 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速开始提示 -->
      <div class="quick-start">
        <div class="quick-start-content">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            {{ quickStartTitle }}
          </h2>
          <div class="quick-start-prompts">
            <button
              v-for="prompt in quickStartPrompts"
              :key="prompt.id"
              class="prompt-button"
              @click="handlePromptClick(prompt.text)"
            >
              <div class="prompt-icon">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
              </div>
              <span>{{ prompt.text }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 使用提示 -->
      <div class="usage-tips">
        <div class="tips-content">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
            💡 使用提示
          </h3>
          <div class="tips-list">
            <div v-for="tip in usageTips" :key="tip.id" class="tip-item">
              <div class="tip-icon">
                <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
              </div>
              <span class="tip-text">{{ tip.text }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="welcome-background">
      <div class="bg-decoration bg-decoration-1"></div>
      <div class="bg-decoration bg-decoration-2"></div>
      <div class="bg-decoration bg-decoration-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'

interface AppInfo {
  id: number
  name: string
  description: string
  avatar?: string
}

interface Feature {
  id: string
  title: string
  description: string
  icon: string
  action?: string
}

interface QuickPrompt {
  id: string
  text: string
}

interface UsageTip {
  id: string
  text: string
}

interface Props {
  mode?: 'teacher' | 'student' | 'app' | 'default'
  appInfo?: AppInfo
}

interface Emits {
  (e: 'use-prompt', prompt: string): void
  (e: 'feature-click', feature: Feature): void
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'default'
})

const emit = defineEmits<Emits>()
const { isMobile } = useBasicLayout()

// 计算属性
const welcomeTitle = computed(() => {
  switch (props.mode) {
    case 'teacher':
      return '欢迎使用AI教学助手'
    case 'student':
      return '开始您的AI学习之旅'
    case 'app':
      return props.appInfo?.name || 'AI应用助手'
    default:
      return '欢迎使用AI助手'
  }
})

const welcomeSubtitle = computed(() => {
  switch (props.mode) {
    case 'teacher':
      return '智能教学，让教育更高效'
    case 'student':
      return '个性化学习，让知识更有趣'
    case 'app':
      return props.appInfo?.description || '专业的AI应用服务'
    default:
      return '智能对话，无限可能'
  }
})

const features = computed<Feature[]>(() => {
  const baseFeatures = [
    {
      id: 'chat',
      title: '智能对话',
      description: '与AI进行自然流畅的对话交流',
      icon: 'ChatIcon',
      action: 'start-chat'
    },
    {
      id: 'file',
      title: '文件分析',
      description: '上传文档、图片等文件进行智能分析',
      icon: 'DocumentIcon',
      action: 'upload-file'
    },
    {
      id: 'code',
      title: '代码助手',
      description: '编程问题解答和代码生成',
      icon: 'CodeIcon',
      action: 'code-help'
    }
  ]

  if (props.mode === 'teacher') {
    return [
      ...baseFeatures,
      {
        id: 'lesson',
        title: '课程设计',
        description: 'AI辅助设计教学课程和教案',
        icon: 'AcademicCapIcon',
        action: 'design-lesson'
      },
      {
        id: 'quiz',
        title: '题目生成',
        description: '自动生成各类练习题和测试题',
        icon: 'QuestionMarkCircleIcon',
        action: 'generate-quiz'
      }
    ]
  }

  if (props.mode === 'student') {
    return [
      ...baseFeatures,
      {
        id: 'study',
        title: '学习辅导',
        description: '个性化学习指导和答疑解惑',
        icon: 'BookOpenIcon',
        action: 'study-help'
      },
      {
        id: 'practice',
        title: '练习题库',
        description: '丰富的练习题目和解题指导',
        icon: 'PencilIcon',
        action: 'practice-questions'
      }
    ]
  }

  return baseFeatures
})

const quickStartPrompts = computed<QuickPrompt[]>(() => {
  const basePrompts = [
    { id: '1', text: '你好，请介绍一下你的功能' },
    { id: '2', text: '帮我写一篇关于人工智能的文章' },
    { id: '3', text: '解释一下量子计算的基本原理' }
  ]

  if (props.mode === 'teacher') {
    return [
      { id: '1', text: '帮我设计一节关于数学函数的课程' },
      { id: '2', text: '生成10道关于英语语法的练习题' },
      { id: '3', text: '制作一个物理实验的教学方案' }
    ]
  }

  if (props.mode === 'student') {
    return [
      { id: '1', text: '帮我理解这道数学题的解题思路' },
      { id: '2', text: '解释一下这个英语语法知识点' },
      { id: '3', text: '总结一下这篇文章的主要内容' }
    ]
  }

  return basePrompts
})

const quickStartTitle = computed(() => {
  switch (props.mode) {
    case 'teacher':
      return '快速开始教学'
    case 'student':
      return '开始学习'
    default:
      return '试试这些问题'
  }
})

const usageTips = computed<UsageTip[]>(() => {
  const baseTips = [
    { id: '1', text: '输入问题后按 Enter 发送，Shift+Enter 换行' },
    { id: '2', text: '可以上传文件让AI帮您分析内容' },
    { id: '3', text: '使用 @ 符号可以搜索特定的AI应用' }
  ]

  if (props.mode === 'teacher') {
    return [
      ...baseTips,
      { id: '4', text: '可以要求AI生成不同难度的题目' },
      { id: '5', text: '支持多种学科的教学内容生成' }
    ]
  }

  if (props.mode === 'student') {
    return [
      ...baseTips,
      { id: '4', text: '遇到不懂的问题可以要求详细解释' },
      { id: '5', text: '可以请AI出题来检验学习效果' }
    ]
  }

  return baseTips
})

// 事件处理
const handlePromptClick = (prompt: string) => {
  emit('use-prompt', prompt)
}

const handleFeatureClick = (feature: Feature) => {
  emit('feature-click', feature)
  
  // 根据功能类型触发相应的提示
  const prompts = {
    'start-chat': '你好，我想开始一个对话',
    'upload-file': '我想上传一个文件让你分析',
    'code-help': '我需要编程方面的帮助',
    'design-lesson': '帮我设计一节课程',
    'generate-quiz': '帮我生成一些练习题',
    'study-help': '我需要学习指导',
    'practice-questions': '给我一些练习题'
  }
  
  const prompt = prompts[feature.action as keyof typeof prompts]
  if (prompt) {
    emit('use-prompt', prompt)
  }
}
</script>

<style scoped>
.welcome-screen {
  @apply relative min-h-full flex items-center justify-center p-6;
}

.welcome-content {
  @apply relative z-10 max-w-4xl mx-auto text-center space-y-12;
}

.welcome-header {
  @apply space-y-6;
}

.welcome-icon {
  @apply flex justify-center;
}

.icon-container {
  @apply p-4 rounded-full shadow-lg;
}

.teacher-icon {
  @apply bg-blue-100 dark:bg-blue-900;
}

.student-icon {
  @apply bg-green-100 dark:bg-green-900;
}

.app-icon {
  @apply bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600;
}

.default-icon {
  @apply bg-purple-100 dark:bg-purple-900;
}

.feature-cards {
  @apply space-y-6;
}

.feature-card {
  @apply flex items-center p-6 bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700;
}

.feature-card:hover {
  @apply transform scale-105 border-blue-300 dark:border-blue-600;
}

.feature-icon {
  @apply flex-shrink-0 p-3 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-lg mr-4;
}

.feature-content {
  @apply flex-1 text-left;
}

.feature-title {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1;
}

.feature-description {
  @apply text-gray-600 dark:text-gray-400;
}

.feature-arrow {
  @apply flex-shrink-0 ml-4;
}

.quick-start {
  @apply bg-gray-50 dark:bg-gray-800 rounded-xl p-6;
}

.quick-start-prompts {
  @apply flex flex-wrap gap-3 justify-center;
}

.prompt-button {
  @apply flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-700 hover:bg-blue-50 dark:hover:bg-blue-900 text-gray-700 dark:text-gray-300 rounded-lg border border-gray-200 dark:border-gray-600 transition-all duration-200 hover:border-blue-300 dark:hover:border-blue-600;
}

.prompt-button:hover {
  @apply transform scale-105;
}

.prompt-icon {
  @apply text-blue-500;
}

.usage-tips {
  @apply bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6;
}

.tips-list {
  @apply space-y-3;
}

.tip-item {
  @apply flex items-center space-x-3 text-left;
}

.tip-icon {
  @apply flex-shrink-0;
}

.tip-text {
  @apply text-gray-700 dark:text-gray-300;
}

.welcome-background {
  @apply absolute inset-0 overflow-hidden pointer-events-none;
}

.bg-decoration {
  @apply absolute rounded-full opacity-10;
}

.bg-decoration-1 {
  @apply w-64 h-64 bg-blue-500 -top-32 -left-32 animate-pulse;
}

.bg-decoration-2 {
  @apply w-48 h-48 bg-purple-500 -bottom-24 -right-24 animate-pulse;
  animation-delay: 1s;
}

.bg-decoration-3 {
  @apply w-32 h-32 bg-pink-500 top-1/2 left-1/4 animate-pulse;
  animation-delay: 2s;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .welcome-content {
    @apply space-y-8;
  }
  
  .feature-card {
    @apply flex-col text-center space-y-4;
  }
  
  .feature-icon {
    @apply mr-0;
  }
  
  .feature-content {
    @apply text-center;
  }
  
  .feature-arrow {
    @apply ml-0;
  }
  
  .quick-start-prompts {
    @apply flex-col;
  }
  
  .prompt-button {
    @apply w-full justify-center;
  }
}
</style>
