<!--
  文件上传组件
  支持拖拽上传、多文件选择、上传进度显示、文件预览等功能
-->
<template>
  <div class="file-upload-container">
    <!-- 文件上传按钮 -->
    <div class="upload-trigger">
      <button
        :disabled="disabled || isUploading"
        class="upload-button"
        :class="[
          'flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200',
          {
            'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300': !disabled,
            'bg-gray-50 dark:bg-gray-800 text-gray-400 cursor-not-allowed': disabled,
            'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400': isUploading
          }
        ]"
        @click="triggerFileSelect"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
      >
        <svg v-if="!isUploading" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
        </svg>
        <div v-else class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </button>

      <!-- 隐藏的文件输入 -->
      <input
        ref="fileInputRef"
        type="file"
        :multiple="allowMultiple"
        :accept="acceptTypes"
        class="hidden"
        @change="handleFileSelect"
      />
    </div>

    <!-- 上传进度 -->
    <div v-if="isUploading && uploadProgress > 0" class="upload-progress mt-2">
      <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
        <span>上传中...</span>
        <span>{{ uploadProgress }}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
        <div
          class="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
          :style="{ width: `${uploadProgress}%` }"
        ></div>
      </div>
    </div>

    <!-- 已选择的文件列表 -->
    <div v-if="selectedFiles.length > 0" class="selected-files mt-3">
      <div class="text-xs text-gray-600 dark:text-gray-400 mb-2">
        已选择 {{ selectedFiles.length }} 个文件
      </div>
      <div class="space-y-2 max-h-32 overflow-y-auto">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="file-item flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <div class="flex items-center space-x-2 flex-1 min-w-0">
            <!-- 文件图标 -->
            <div class="flex-shrink-0">
              <svg v-if="isImageFile(file)" class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
              </svg>
              <svg v-else-if="isDocumentFile(file)" class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"/>
              </svg>
              <svg v-else class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
              </svg>
            </div>

            <!-- 文件信息 -->
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {{ file.name }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatFileSize(file.size) }}
              </div>
            </div>
          </div>

          <!-- 删除按钮 -->
          <button
            class="flex-shrink-0 ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors duration-200"
            @click="removeFile(index)"
          >
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 拖拽覆盖层 -->
    <div
      v-if="isDragOver"
      class="drag-overlay fixed inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center z-50"
      @dragover.prevent
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <div class="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-xl border-2 border-dashed border-blue-500">
        <div class="text-center">
          <svg class="w-12 h-12 text-blue-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
          </svg>
          <div class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            释放以上传文件
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            支持 {{ acceptTypes || '所有类型' }} 文件
          </div>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message mt-2">
      <div class="text-sm text-red-600 dark:text-red-400 flex items-center space-x-2">
        <svg class="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
        </svg>
        <span>{{ errorMessage }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'

interface Props {
  disabled?: boolean
  allowMultiple?: boolean
  maxSize?: number // 最大文件大小（字节）
  maxFiles?: number // 最大文件数量
  acceptTypes?: string // 接受的文件类型
  allowedExtensions?: string[] // 允许的文件扩展名
}

interface Emits {
  (e: 'upload', files: File[]): void
  (e: 'remove', index: number): void
  (e: 'clear'): void
  (e: 'error', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  allowMultiple: true,
  maxSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5,
  acceptTypes: '',
  allowedExtensions: () => []
})

const emit = defineEmits<Emits>()
const message = useMessage()

// 响应式数据
const fileInputRef = ref<HTMLInputElement>()
const selectedFiles = ref<File[]>([])
const isUploading = ref(false)
const uploadProgress = ref(0)
const isDragOver = ref(false)
const errorMessage = ref('')

// 计算属性
const canUpload = computed(() => {
  return !props.disabled && !isUploading.value
})

// 方法
const triggerFileSelect = () => {
  if (canUpload.value) {
    fileInputRef.value?.click()
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = Array.from(target.files || [])
  processFiles(files)
  // 清空input值，允许重复选择同一文件
  target.value = ''
}

const handleDragOver = (event: DragEvent) => {
  if (canUpload.value) {
    isDragOver.value = true
  }
}

const handleDragLeave = (event: DragEvent) => {
  // 检查是否真的离开了拖拽区域
  if (!event.relatedTarget || !event.currentTarget?.contains(event.relatedTarget as Node)) {
    isDragOver.value = false
  }
}

const handleDrop = (event: DragEvent) => {
  isDragOver.value = false
  if (!canUpload.value) return

  const files = Array.from(event.dataTransfer?.files || [])
  processFiles(files)
}

const processFiles = (files: File[]) => {
  if (!files.length) return

  // 清除之前的错误
  errorMessage.value = ''

  // 验证文件
  const validFiles: File[] = []

  for (const file of files) {
    // 检查文件大小
    if (file.size > props.maxSize) {
      setError(`文件 "${file.name}" 大小超过限制 (${formatFileSize(props.maxSize)})`)
      continue
    }

    // 检查文件类型
    if (props.allowedExtensions.length > 0) {
      const extension = file.name.split('.').pop()?.toLowerCase()
      if (!extension || !props.allowedExtensions.includes(extension)) {
        setError(`不支持的文件类型: ${file.name}`)
        continue
      }
    }

    validFiles.push(file)
  }

  if (!validFiles.length) return

  // 检查文件数量限制
  const totalFiles = selectedFiles.value.length + validFiles.length
  if (totalFiles > props.maxFiles) {
    setError(`最多只能选择 ${props.maxFiles} 个文件`)
    return
  }

  // 添加到已选择列表
  if (props.allowMultiple) {
    selectedFiles.value.push(...validFiles)
  } else {
    selectedFiles.value = [validFiles[0]]
  }

  // 触发上传事件
  emit('upload', validFiles)
  message.success(`已选择 ${validFiles.length} 个文件`)
}

const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
  emit('remove', index)
}

const clearFiles = () => {
  selectedFiles.value = []
  emit('clear')
}

const setError = (msg: string) => {
  errorMessage.value = msg
  emit('error', msg)
  setTimeout(() => {
    errorMessage.value = ''
  }, 5000)
}

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const isImageFile = (file: File): boolean => {
  return file.type.startsWith('image/')
}

const isDocumentFile = (file: File): boolean => {
  const documentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ]
  return documentTypes.includes(file.type)
}

// 模拟上传进度
const simulateUpload = () => {
  isUploading.value = true
  uploadProgress.value = 0

  const interval = setInterval(() => {
    uploadProgress.value += Math.random() * 20
    if (uploadProgress.value >= 100) {
      uploadProgress.value = 100
      clearInterval(interval)
      setTimeout(() => {
        isUploading.value = false
        uploadProgress.value = 0
      }, 500)
    }
  }, 200)
}

// 暴露方法给父组件
defineExpose({
  clearFiles,
  selectedFiles: computed(() => selectedFiles.value),
  triggerFileSelect
})
</script>

<style scoped>
.file-upload-container {
  @apply relative;
}

.upload-button {
  @apply transition-all duration-200 ease-in-out;
}

.upload-button:hover:not(:disabled) {
  @apply transform scale-105;
}

.file-item {
  @apply transition-all duration-200;
}

.file-item:hover {
  @apply bg-gray-100 dark:bg-gray-600;
}

.drag-overlay {
  animation: fadeIn 0.2s ease-out;
}

.upload-progress {
  animation: slideInFromTop 0.2s ease-out;
}

.error-message {
  animation: slideInFromTop 0.2s ease-out;
}

.selected-files {
  animation: slideInFromTop 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义滚动条 */
.selected-files .space-y-2::-webkit-scrollbar {
  @apply w-1;
}

.selected-files .space-y-2::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700 rounded;
}

.selected-files .space-y-2::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-500 rounded;
}

.selected-files .space-y-2::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-400;
}
</style>
